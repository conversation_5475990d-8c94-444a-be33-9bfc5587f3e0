package fulfillment_center

import (
	"bytes"
	"context"
	"encoding/json"
	"fmt"

	"webapi/dal/query"
	"webapi/datasource"
	"webapi/datasource/config"
	"webapi/internal/svc"
	"webapi/internal/types"

	"github.com/zeromicro/go-zero/core/logx"
)

type UpdateDeliverItemSalesTransactionLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewUpdateDeliverItemSalesTransactionLogic(ctx context.Context, svcCtx *svc.ServiceContext) *UpdateDeliverItemSalesTransactionLogic {
	return &UpdateDeliverItemSalesTransactionLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *UpdateDeliverItemSalesTransactionLogic) UpdateDeliverItemSalesTransaction(req *types.UpdateDeliverItemSalesTransactionReq) (resp *types.UpdateDeliverItemSalesTransactionResp, err error) {
	// 定义销售交易主体结构
	type SalesTransactionItem struct {
		Sku                 string `json:"sku"`
		Quantity            int    `json:"quantity"`
		SubjectCode         string `json:"subjectCode"`
		TransactionLinkCode string `json:"transactionLinkCode"`
	}

	// 1. 根据 rel_order_no 查找 deliver 记录
	db := datasource.GetDsCtxOrDefault(l.ctx, config.SiteFulfillment)
	q := query.Use(db)

	// 查找 deliver 记录
	delivers, err := q.Deliver.WithContext(l.ctx).Where(q.Deliver.Code.Eq(req.RelOrderNo)).Find()
	if err != nil {
		l.Errorf("查找 deliver 记录失败: %v", err)
		return &types.UpdateDeliverItemSalesTransactionResp{
			Success: false,
			Message: fmt.Sprintf("查找 deliver 记录失败: %v", err),
		}, nil
	}

	if len(delivers) == 0 {
		return &types.UpdateDeliverItemSalesTransactionResp{
			Success: false,
			Message: fmt.Sprintf("未找到 rel_order_no 为 %s 的 deliver 记录", req.RelOrderNo),
		}, nil
	}

	// 2. 根据 deliver_id 和 sku 查找 deliver_item 记录
	var deliverIDs []int32
	for _, deliver := range delivers {
		deliverIDs = append(deliverIDs, deliver.ID)
	}

	deliverItems, err := q.DeliverItem.WithContext(l.ctx).
		Where(q.DeliverItem.DeliverID.In(deliverIDs...)).
		Where(q.DeliverItem.Sku.Eq(req.Sku)).
		Find()
	if err != nil {
		l.Errorf("查找 deliver_item 记录失败: %v", err)
		return &types.UpdateDeliverItemSalesTransactionResp{
			Success: false,
			Message: fmt.Sprintf("查找 deliver_item 记录失败: %v", err),
		}, nil
	}

	if len(deliverItems) == 0 {
		return &types.UpdateDeliverItemSalesTransactionResp{
			Success: false,
			Message: fmt.Sprintf("未找到 rel_order_no 为 %s, sku 为 %s 的 deliver_item 记录", req.RelOrderNo, req.Sku),
		}, nil
	}

	// 3. 处理每个 deliver_item 记录
	updatedCount := 0
	for _, item := range deliverItems {
		// 解析现有的 sales_transaction_master
		var existingTransactions []SalesTransactionItem
		if item.SalesTransactionMaster != "" {
			if err := json.Unmarshal([]byte(item.SalesTransactionMaster), &existingTransactions); err != nil {
				l.Errorf("解析现有 sales_transaction_master 失败: %v", err)
				continue
			}
		}

		// 创建新的交易项
		newTransaction := SalesTransactionItem{
			Sku:         req.Sku,
			Quantity:    req.ChangeQuantity,
			SubjectCode: req.SubjectCode,
			// 不处理 TransactionLinkCode，保持为空
			TransactionLinkCode: "",
		}

		// 使用 map 进行去重合并
		transactionMap := make(map[string]SalesTransactionItem)

		// 先添加现有交易
		for _, tx := range existingTransactions {
			// 数量不同的认为是新的记录，不用合并，直接添加
			// 使用更详细的key来区分不同数量的记录
			detailedKey := fmt.Sprintf("%s-%s-%d", tx.Sku, tx.SubjectCode, tx.Quantity)
			transactionMap[detailedKey] = tx
		}

		// 添加新交易
		// 数量不同的认为是新的记录，不用合并，直接添加
		newDetailedKey := fmt.Sprintf("%s-%s-%d", newTransaction.Sku, newTransaction.SubjectCode, newTransaction.Quantity)
		transactionMap[newDetailedKey] = newTransaction

		// 转换回数组
		var mergedTransactions []SalesTransactionItem
		for _, tx := range transactionMap {
			mergedTransactions = append(mergedTransactions, tx)
		}

		// 序列化为 JSON，不带转义符号
		var buffer bytes.Buffer
		encoder := json.NewEncoder(&buffer)
		encoder.SetEscapeHTML(false) // 不转义HTML字符
		err := encoder.Encode(mergedTransactions)
		if err != nil {
			l.Errorf("序列化 sales_transaction_master 失败: %v", err)
			continue
		}
		// 移除末尾的换行符
		mergedJSON := bytes.TrimSpace(buffer.Bytes())

		// 更新数据库
		_, err = q.DeliverItem.WithContext(l.ctx).
			Where(q.DeliverItem.ID.Eq(item.ID)).
			Update(q.DeliverItem.SalesTransactionMaster, string(mergedJSON))
		if err != nil {
			l.Errorf("更新 deliver_item 记录失败: %v", err)
			continue
		}

		updatedCount++
		l.Infof("成功更新 deliver_item ID: %d, sales_transaction_master: %s", item.ID, string(mergedJSON))
	}

	return &types.UpdateDeliverItemSalesTransactionResp{
		Success: true,
		Message: fmt.Sprintf("成功更新 %d 条 deliver_item 记录", updatedCount),
	}, nil
}
