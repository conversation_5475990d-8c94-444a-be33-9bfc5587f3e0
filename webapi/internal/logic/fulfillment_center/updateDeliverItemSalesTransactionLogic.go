package fulfillment_center

import (
	"context"
	"encoding/json"
	"fmt"

	"webapi/dal/query"
	"webapi/datasource"
	"webapi/datasource/config"
	"webapi/internal/svc"
	"webapi/internal/types"

	"github.com/zeromicro/go-zero/core/logx"
)

type UpdateDeliverItemSalesTransactionLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewUpdateDeliverItemSalesTransactionLogic(ctx context.Context, svcCtx *svc.ServiceContext) *UpdateDeliverItemSalesTransactionLogic {
	return &UpdateDeliverItemSalesTransactionLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *UpdateDeliverItemSalesTransactionLogic) UpdateDeliverItemSalesTransaction(req *types.UpdateDeliverItemSalesTransactionReq) (resp *types.UpdateDeliverItemSalesTransactionResp, err error) {
	// 定义销售交易主体结构
	type SalesTransactionItem struct {
		Sku                 string `json:"sku"`
		Quantity            int    `json:"quantity"`
		SubjectCode         string `json:"subjectCode"`
		TransactionLinkCode string `json:"transactionLinkCode"`
	}

	// 1. 根据 rel_order_no 查找 deliver 记录
	db := datasource.GetDsCtxOrDefault(l.ctx, config.SiteFulfillment)
	q := query.Use(db)

	// 查找 deliver 记录
	delivers, err := q.Deliver.WithContext(l.ctx).Where(q.Deliver.Code.Eq(req.RelOrderNo)).Find()
	if err != nil {
		l.Errorf("查找 deliver 记录失败: %v", err)
		return &types.UpdateDeliverItemSalesTransactionResp{
			Success: false,
			Message: fmt.Sprintf("查找 deliver 记录失败: %v", err),
		}, nil
	}

	if len(delivers) == 0 {
		return &types.UpdateDeliverItemSalesTransactionResp{
			Success: false,
			Message: fmt.Sprintf("未找到 rel_order_no 为 %s 的 deliver 记录", req.RelOrderNo),
		}, nil
	}

	// 2. 根据 deliver_id 和 sku 查找 deliver_item 记录
	var deliverIDs []int32
	for _, deliver := range delivers {
		deliverIDs = append(deliverIDs, deliver.ID)
	}

	deliverItems, err := q.DeliverItem.WithContext(l.ctx).
		Where(q.DeliverItem.DeliverID.In(deliverIDs...)).
		Where(q.DeliverItem.Sku.Eq(req.Sku)).
		Find()
	if err != nil {
		l.Errorf("查找 deliver_item 记录失败: %v", err)
		return &types.UpdateDeliverItemSalesTransactionResp{
			Success: false,
			Message: fmt.Sprintf("查找 deliver_item 记录失败: %v", err),
		}, nil
	}

	if len(deliverItems) == 0 {
		return &types.UpdateDeliverItemSalesTransactionResp{
			Success: false,
			Message: fmt.Sprintf("未找到 rel_order_no 为 %s, sku 为 %s 的 deliver_item 记录", req.RelOrderNo, req.Sku),
		}, nil
	}

	// 3. 处理每个 deliver_item 记录
	updatedCount := 0
	for _, item := range deliverItems {
		// 解析现有的 sales_transaction_master
		var existingTransactions []SalesTransactionItem
		if item.SalesTransactionMaster != "" {
			if err := json.Unmarshal([]byte(item.SalesTransactionMaster), &existingTransactions); err != nil {
				l.Errorf("解析现有 sales_transaction_master 失败: %v", err)
				continue
			}
		}

		// 创建新的交易项
		newTransaction := SalesTransactionItem{
			Sku:         req.Sku,
			Quantity:    req.ChangeQuantity,
			SubjectCode: req.SubjectCode,
			// todo 不处理
			TransactionLinkCode: fmt.Sprintf("TC%s-%d", req.CreateTime[:10], item.ID), // 生成交易链路代码
		}

		// 使用 map 进行去重合并
		transactionMap := make(map[string]SalesTransactionItem)

		// 先添加现有交易
		for _, tx := range existingTransactions {
			key := fmt.Sprintf("%s-%s", tx.Sku, tx.SubjectCode)
			if existing, exists := transactionMap[key]; exists {
				// todo 数量不同的认为是新的记录，不用合并
				// 如果已存在相同 sku 和 subjectCode 的记录，合并数量
				existing.Quantity += tx.Quantity
				transactionMap[key] = existing
			} else {
				transactionMap[key] = tx
			}
		}

		// 添加新交易
		newKey := fmt.Sprintf("%s-%s", newTransaction.Sku, newTransaction.SubjectCode)
		if existing, exists := transactionMap[newKey]; exists {
			// 如果已存在相同 sku 和 subjectCode 的记录，合并数量
			existing.Quantity += newTransaction.Quantity
			transactionMap[newKey] = existing
		} else {
			transactionMap[newKey] = newTransaction
		}

		// 转换回数组
		var mergedTransactions []SalesTransactionItem
		for _, tx := range transactionMap {
			mergedTransactions = append(mergedTransactions, tx)
		}

		// 序列化为 JSON
		// todo mergedJSON 不要带有转义符号
		mergedJSON, err := json.Marshal(mergedTransactions)
		if err != nil {
			l.Errorf("序列化 sales_transaction_master 失败: %v", err)
			continue
		}

		// 更新数据库
		_, err = q.DeliverItem.WithContext(l.ctx).
			Where(q.DeliverItem.ID.Eq(item.ID)).
			Update(q.DeliverItem.SalesTransactionMaster, string(mergedJSON))
		if err != nil {
			l.Errorf("更新 deliver_item 记录失败: %v", err)
			continue
		}

		updatedCount++
		l.Infof("成功更新 deliver_item ID: %d, sales_transaction_master: %s", item.ID, string(mergedJSON))
	}

	return &types.UpdateDeliverItemSalesTransactionResp{
		Success: true,
		Message: fmt.Sprintf("成功更新 %d 条 deliver_item 记录", updatedCount),
	}, nil
}
