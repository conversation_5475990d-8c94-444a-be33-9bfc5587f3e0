// Code generated by goctl. DO NOT EDIT.
// goctl 1.8.4

package handler

import (
	"net/http"

	acl "webapi/internal/handler/acl"
	analysis "webapi/internal/handler/analysis"
	approval "webapi/internal/handler/approval"
	fulfillment_center "webapi/internal/handler/fulfillment_center"
	i18n "webapi/internal/handler/i18n"
	ohsome "webapi/internal/handler/ohsome"
	ops "webapi/internal/handler/ops"
	release "webapi/internal/handler/release"
	team "webapi/internal/handler/team"
	"webapi/internal/svc"

	"github.com/zeromicro/go-zero/rest"
)

func RegisterHandlers(server *rest.Server, serverCtx *svc.ServiceContext) {
	server.AddRoutes(
		[]rest.Route{
			{
				Method:  http.MethodPost,
				Path:    "/auth/login",
				Handler: LoginHandler(serverCtx),
			},
			{
				Method:  http.MethodGet,
				Path:    "/auth/tiktok/:app_key/consent",
				Handler: tiktokOAuthConsentHandler(serverCtx),
			},
			{
				Method:  http.MethodGet,
				Path:    "/auth/tiktok/:app_key/redirect",
				Handler: tiktokOAuthRedirectHandler(serverCtx),
			},
			{
				Method:  http.MethodGet,
				Path:    "/auth/shopee/:app_key/redirect",
				Handler: shopeeOAuthRedirectHandler(serverCtx),
			},
			{
				Method:  http.MethodGet,
				Path:    "/auth/lazada/:app_key/consent",
				Handler: lazadaOAuthConsentHandler(serverCtx),
			},
			{
				Method:  http.MethodGet,
				Path:    "/auth/lazada/:app_key/redirect",
				Handler: lazadaOAuthRedirectHandler(serverCtx),
			},
		},
	)

	server.AddRoutes(
		rest.WithMiddlewares(
			[]rest.Middleware{serverCtx.AuthInterceptor},
			[]rest.Route{
				{
					Method:  http.MethodGet,
					Path:    "/user/info",
					Handler: fulfillment_center.GetUserInfoHandler(serverCtx),
				},
				{
					Method:  http.MethodGet,
					Path:    "/fulfillment/preferences",
					Handler: fulfillment_center.GetFulfillmentPreferencesHandler(serverCtx),
				},
				{
					Method:  http.MethodPut,
					Path:    "/fulfillment/preferences",
					Handler: fulfillment_center.UpdateFulfillmentPreferencesHandler(serverCtx),
				},
				{
					Method:  http.MethodGet,
					Path:    "/tour-migrations",
					Handler: fulfillment_center.GetTourMigrationsHandler(serverCtx),
				},
				{
					Method:  http.MethodPut,
					Path:    "/tour-migrations/:region",
					Handler: fulfillment_center.UpdateTourMigrationsHandler(serverCtx),
				},
				{
					Method:  http.MethodGet,
					Path:    "/scm/inventory-items",
					Handler: fulfillment_center.GetScmInventoryItemsHandler(serverCtx),
				},
				{
					Method:  http.MethodGet,
					Path:    "/scm/stocks",
					Handler: fulfillment_center.GetScmStocksHandler(serverCtx),
				},
				{
					Method:  http.MethodGet,
					Path:    "/scm/request-audit-logs",
					Handler: fulfillment_center.GetScmRequestAuditLogsHandler(serverCtx),
				},
				{
					Method:  http.MethodPost,
					Path:    "/scm/push-order",
					Handler: fulfillment_center.PushOrderToScmHandler(serverCtx),
				},
				{
					Method:  http.MethodPost,
					Path:    "/scm/push-order-without-cancel",
					Handler: fulfillment_center.PushOrderToScmWithoutCancelHandler(serverCtx),
				},
				{
					Method:  http.MethodGet,
					Path:    "/warehouses",
					Handler: fulfillment_center.GetWarehousesHandler(serverCtx),
				},
				{
					Method:  http.MethodPost,
					Path:    "/warehouses",
					Handler: fulfillment_center.CreateWarehouseHandler(serverCtx),
				},
				{
					Method:  http.MethodPut,
					Path:    "/warehouses/:id",
					Handler: fulfillment_center.UpdateWarehouseHandler(serverCtx),
				},
				{
					Method:  http.MethodGet,
					Path:    "/metrics/redis-queue",
					Handler: fulfillment_center.GetRedisMetricsHandler(serverCtx),
				},
				{
					Method:  http.MethodGet,
					Path:    "/metrics/orders",
					Handler: fulfillment_center.GetOrdersMetricsHandler(serverCtx),
				},
				{
					Method:  http.MethodGet,
					Path:    "/users",
					Handler: fulfillment_center.GetUsersHandler(serverCtx),
				},
				{
					Method:  http.MethodPost,
					Path:    "/users",
					Handler: fulfillment_center.CreateUserHandler(serverCtx),
				},
				{
					Method:  http.MethodPost,
					Path:    "/users/:id/disable",
					Handler: fulfillment_center.DisableUserHandler(serverCtx),
				},
				{
					Method:  http.MethodPost,
					Path:    "/users/:id/password",
					Handler: fulfillment_center.ResetPasswordHandler(serverCtx),
				},
				{
					Method:  http.MethodPost,
					Path:    "/users/:id/enable",
					Handler: fulfillment_center.EnableUserHandler(serverCtx),
				},
				{
					Method:  http.MethodGet,
					Path:    "/maintenance/redis-queues",
					Handler: fulfillment_center.GetRedisQueuesHandler(serverCtx),
				},
				{
					Method:  http.MethodDelete,
					Path:    "/maintenance/redis-queues/:channel/item",
					Handler: fulfillment_center.DeleteQueueItemHandler(serverCtx),
				},
				{
					Method:  http.MethodGet,
					Path:    "/shops",
					Handler: fulfillment_center.GetShopsHandler(serverCtx),
				},
				{
					Method:  http.MethodPost,
					Path:    "/shops",
					Handler: fulfillment_center.CreateShopHandler(serverCtx),
				},
				{
					Method:  http.MethodPost,
					Path:    "/shops/:id/sync",
					Handler: fulfillment_center.SyncShopHandler(serverCtx),
				},
				{
					Method:  http.MethodPut,
					Path:    "/shops/:id",
					Handler: fulfillment_center.UpdateShopHandler(serverCtx),
				},
				{
					Method:  http.MethodPatch,
					Path:    "/regions/:region/shops/:id/status",
					Handler: fulfillment_center.PatchShopStatusHandler(serverCtx),
				},
				{
					Method:  http.MethodGet,
					Path:    "/tiktok-orders/:id",
					Handler: fulfillment_center.GetTikTokOrderDetailHandler(serverCtx),
				},
				{
					Method:  http.MethodGet,
					Path:    "/tiktok-stocks",
					Handler: fulfillment_center.GetTikTokStocksHandler(serverCtx),
				},
				{
					Method:  http.MethodGet,
					Path:    "/shopee-orders/:id",
					Handler: fulfillment_center.GetShopeeOrderDetailHandler(serverCtx),
				},
				{
					Method:  http.MethodGet,
					Path:    "/shopee-stocks",
					Handler: fulfillment_center.GetShopeeStocksHandler(serverCtx),
				},
				{
					Method:  http.MethodGet,
					Path:    "/lazada-orders/:id",
					Handler: fulfillment_center.GetLazadaOrderDetailHandler(serverCtx),
				},
				{
					Method:  http.MethodGet,
					Path:    "/lazada-stocks",
					Handler: fulfillment_center.GetLazadaStocksHandler(serverCtx),
				},
				{
					Method:  http.MethodGet,
					Path:    "/tokopedia-orders/:id",
					Handler: fulfillment_center.GetTokopediaOrderDetailHandler(serverCtx),
				},
				{
					Method:  http.MethodGet,
					Path:    "/tokopedia-stocks",
					Handler: fulfillment_center.GetTokopediaStocksHandler(serverCtx),
				},
				{
					Method:  http.MethodGet,
					Path:    "/shopline-orders/:id",
					Handler: fulfillment_center.GetShoplineOrderDetailHandler(serverCtx),
				},
				{
					Method:  http.MethodGet,
					Path:    "/shopline-stocks",
					Handler: fulfillment_center.GetShoplineStocksHandler(serverCtx),
				},
				{
					Method:  http.MethodGet,
					Path:    "/region/:region/shopee/:shop_id/products",
					Handler: fulfillment_center.GetShopeeProductsHandler(serverCtx),
				},
				{
					Method:  http.MethodGet,
					Path:    "/region/:region/lazada/:shop_id/products",
					Handler: fulfillment_center.GetLazadaProductsHandler(serverCtx),
				},
				{
					Method:  http.MethodGet,
					Path:    "/region/:region/tiktok/:shop_id/products",
					Handler: fulfillment_center.GetTikTokProductsHandler(serverCtx),
				},
				{
					Method:  http.MethodGet,
					Path:    "/shop-authorizations",
					Handler: fulfillment_center.GetShopAuthorizationsHandler(serverCtx),
				},
				{
					Method:  http.MethodPost,
					Path:    "/shop-authorizations",
					Handler: fulfillment_center.CreateShopAuthorizationsHandler(serverCtx),
				},
				{
					Method:  http.MethodGet,
					Path:    "/shop-apps",
					Handler: fulfillment_center.GetShopAppsHandler(serverCtx),
				},
				{
					Method:  http.MethodPost,
					Path:    "/shop-apps",
					Handler: fulfillment_center.AddShopAppsHandler(serverCtx),
				},
				{
					Method:  http.MethodPost,
					Path:    "/wms/inbound-orders",
					Handler: fulfillment_center.GetWmsInboundOrdersHandler(serverCtx),
				},
				{
					Method:  http.MethodPost,
					Path:    "/wms/outbound-orders",
					Handler: fulfillment_center.GetWmsOutboundOrdersHandler(serverCtx),
				},
				{
					Method:  http.MethodGet,
					Path:    "/orders/ids",
					Handler: fulfillment_center.GetOrderIdsHandler(serverCtx),
				},
				{
					Method:  http.MethodGet,
					Path:    "/orders",
					Handler: fulfillment_center.GetOrdersHandler(serverCtx),
				},
				{
					Method:  http.MethodGet,
					Path:    "/orders/delivery-codes",
					Handler: fulfillment_center.GetOrderDeliveryCodesHandler(serverCtx),
				},
				{
					Method:  http.MethodGet,
					Path:    "/orders/return-ids",
					Handler: fulfillment_center.GetReturnOrderIdsHandler(serverCtx),
				},
				{
					Method:  http.MethodGet,
					Path:    "/orders/state-metrics",
					Handler: fulfillment_center.GetOrderStateMetricsHandler(serverCtx),
				},
				{
					Method:  http.MethodGet,
					Path:    "/orders/shop-state-metrics",
					Handler: fulfillment_center.GetOrderShopStateMetricsHandler(serverCtx),
				},
				{
					Method:  http.MethodGet,
					Path:    "/shops/:id",
					Handler: fulfillment_center.GetShopHandler(serverCtx),
				},
				{
					Method:  http.MethodPost,
					Path:    "/products/image-urls",
					Handler: fulfillment_center.GetProductImageUrlsHandler(serverCtx),
				},
				{
					Method:  http.MethodPost,
					Path:    "/maintenance/fix-skus-images",
					Handler: fulfillment_center.FixSkusImagesHandler(serverCtx),
				},
				{
					Method:  http.MethodGet,
					Path:    "/orders/without-tracking",
					Handler: fulfillment_center.GetOrdersWithoutTrackingHandler(serverCtx),
				},
				{
					Method:  http.MethodGet,
					Path:    "/orders/without-delivery",
					Handler: fulfillment_center.GetOrdersWithoutDeliveryHandler(serverCtx),
				},
				{
					Method:  http.MethodGet,
					Path:    "/orders/with-stock-issue",
					Handler: fulfillment_center.GetOrdersWithStockIssueHandler(serverCtx),
				},
				{
					Method:  http.MethodGet,
					Path:    "/orders/with-combine-sku-issue",
					Handler: fulfillment_center.GetOrdersWithCombineSkuIssueHandler(serverCtx),
				},
				{
					Method:  http.MethodGet,
					Path:    "/orders/sync-metrics",
					Handler: fulfillment_center.GetOrderSyncMetricsHandler(serverCtx),
				},
				{
					Method:  http.MethodPost,
					Path:    "/maintenance/init-search-terms-config",
					Handler: fulfillment_center.InitSearchTermsConfigHandler(serverCtx),
				},
				{
					Method:  http.MethodGet,
					Path:    "/financial/sales-entity",
					Handler: fulfillment_center.GetFinancialSalesEntityHandler(serverCtx),
				},
				{
					Method:  http.MethodPost,
					Path:    "/financial/sales-entity-by-order-ids",
					Handler: fulfillment_center.GetFinancialSalesEntityByOrderIdsHandler(serverCtx),
				},
				{
					Method:  http.MethodPost,
					Path:    "/shops/:id/init-product",
					Handler: fulfillment_center.InitShopProductHandler(serverCtx),
				},
				{
					Method:  http.MethodGet,
					Path:    "/site-config",
					Handler: fulfillment_center.GetSiteConfigHandler(serverCtx),
				},
				{
					Method:  http.MethodPut,
					Path:    "/site-config",
					Handler: fulfillment_center.UpdateSiteConfigHandler(serverCtx),
				},
				{
					Method:  http.MethodDelete,
					Path:    "/site-config/:region",
					Handler: fulfillment_center.DeleteSiteConfigHandler(serverCtx),
				},
				{
					Method:  http.MethodGet,
					Path:    "/maintenance/queue-consumption",
					Handler: fulfillment_center.GetQueueConsumptionStatusHandler(serverCtx),
				},
				{
					Method:  http.MethodPut,
					Path:    "/maintenance/queue-consumption",
					Handler: fulfillment_center.UpdateQueueConsumptionStatusHandler(serverCtx),
				},
				{
					Method:  http.MethodPost,
					Path:    "/maintenance/sync-order-warehouse-state",
					Handler: fulfillment_center.SyncOrderWarehouseStateHandler(serverCtx),
				},
				{
					Method:  http.MethodPost,
					Path:    "/maintenance/:region/orders/:deliveryCode/sync-warehouse-state",
					Handler: fulfillment_center.SyncSingleOrderWarehouseStateHandler(serverCtx),
				},
				{
					Method:  http.MethodPost,
					Path:    "/maintenance/:region/shops/:shop_id/orders/:order_id/sync",
					Handler: fulfillment_center.SyncShopOrderWithChannelHandler(serverCtx),
				},
				{
					Method:  http.MethodGet,
					Path:    "/warehouse-stock-syncs",
					Handler: fulfillment_center.GetWarehouseStockSyncsHandler(serverCtx),
				},
				{
					Method:  http.MethodPost,
					Path:    "/warehouse-stock-syncs",
					Handler: fulfillment_center.CreateWarehouseStockSyncHandler(serverCtx),
				},
				{
					Method:  http.MethodPut,
					Path:    "/regions/:region/warehouse-stock-syncs/:id",
					Handler: fulfillment_center.UpdateWarehouseStockSyncHandler(serverCtx),
				},
				{
					Method:  http.MethodDelete,
					Path:    "/regions/:region/warehouse-stock-syncs/:id",
					Handler: fulfillment_center.DeleteWarehouseStockSyncHandler(serverCtx),
				},
				{
					Method:  http.MethodPatch,
					Path:    "/regions/:region/return-request-migration/:warehouse_in_no",
					Handler: fulfillment_center.ReturnMerchandiseBackWarehouseHandler(serverCtx),
				},
				{
					Method:  http.MethodPost,
					Path:    "/regions/:region/maintenance/deliver-item/sales-transaction",
					Handler: fulfillment_center.UpdateDeliverItemSalesTransactionHandler(serverCtx),
				},
			}...,
		),
		rest.WithPrefix("/api"),
	)

	server.AddRoutes(
		rest.WithMiddlewares(
			[]rest.Middleware{serverCtx.AuthInterceptor},
			[]rest.Route{
				{
					Method:  http.MethodGet,
					Path:    "/translations",
					Handler: i18n.GetTranslationsHandler(serverCtx),
				},
				{
					Method:  http.MethodPost,
					Path:    "/translations",
					Handler: i18n.CreateTranslationHandler(serverCtx),
				},
				{
					Method:  http.MethodGet,
					Path:    "/translations/:id",
					Handler: i18n.GetTranslationHandler(serverCtx),
				},
				{
					Method:  http.MethodPut,
					Path:    "/translations/:id",
					Handler: i18n.UpdateTranslationHandler(serverCtx),
				},
				{
					Method:  http.MethodDelete,
					Path:    "/translations/:id",
					Handler: i18n.DeleteTranslationHandler(serverCtx),
				},
				{
					Method:  http.MethodGet,
					Path:    "/translation-values",
					Handler: i18n.GetTranslationValuesHandler(serverCtx),
				},
				{
					Method:  http.MethodPost,
					Path:    "/translation-values",
					Handler: i18n.CreateTranslationValueHandler(serverCtx),
				},
				{
					Method:  http.MethodGet,
					Path:    "/translation-values/:id",
					Handler: i18n.GetTranslationValueHandler(serverCtx),
				},
				{
					Method:  http.MethodPut,
					Path:    "/translation-values/:id",
					Handler: i18n.UpdateTranslationValueHandler(serverCtx),
				},
				{
					Method:  http.MethodDelete,
					Path:    "/translation-values/:id",
					Handler: i18n.DeleteTranslationValueHandler(serverCtx),
				},
				{
					Method:  http.MethodGet,
					Path:    "/translations/export",
					Handler: i18n.ExportTranslationsHandler(serverCtx),
				},
				{
					Method:  http.MethodPost,
					Path:    "/translations/:id/ai-translate",
					Handler: i18n.GetAITranslationHandler(serverCtx),
				},
				{
					Method:  http.MethodPost,
					Path:    "/translations/generate-key",
					Handler: i18n.GenerateTranslationKeyHandler(serverCtx),
				},
				{
					Method:  http.MethodGet,
					Path:    "/translations/check-key",
					Handler: i18n.CheckTranslationKeyHandler(serverCtx),
				},
				{
					Method:  http.MethodPost,
					Path:    "/translations/import-from-error-codes",
					Handler: i18n.ImportFromErrorCodesHandler(serverCtx),
				},
			}...,
		),
		rest.WithPrefix("/api/i18n"),
	)

	server.AddRoutes(
		rest.WithMiddlewares(
			[]rest.Middleware{serverCtx.AuthInterceptor},
			[]rest.Route{
				{
					Method:  http.MethodPost,
					Path:    "/influencer-videos",
					Handler: analysis.CreateInfluencerVideoHandler(serverCtx),
				},
				{
					Method:  http.MethodGet,
					Path:    "/influencer-videos/:id",
					Handler: analysis.GetInfluencerVideoHandler(serverCtx),
				},
				{
					Method:  http.MethodPatch,
					Path:    "/influencer-videos/:id",
					Handler: analysis.PatchInfluencerVideoHandler(serverCtx),
				},
				{
					Method:  http.MethodPatch,
					Path:    "/influencer-videos/:id/hash",
					Handler: analysis.PatchInfluencerVideoHashHandler(serverCtx),
				},
				{
					Method:  http.MethodPost,
					Path:    "/influencer-videos/:id/daily-metrics",
					Handler: analysis.CreateInfluencerVideoDailyMetricsHandler(serverCtx),
				},
				{
					Method:  http.MethodGet,
					Path:    "/influencer-videos",
					Handler: analysis.GetInfluencerVideosHandler(serverCtx),
				},
				{
					Method:  http.MethodPut,
					Path:    "/influencer-videos/:id",
					Handler: analysis.UpdateInfluencerVideoHandler(serverCtx),
				},
				{
					Method:  http.MethodDelete,
					Path:    "/influencer-videos/:id",
					Handler: analysis.DeleteInfluencerVideoHandler(serverCtx),
				},
				{
					Method:  http.MethodGet,
					Path:    "/influencer-gmv-summary",
					Handler: analysis.GetInfluencerGmvSummaryHandler(serverCtx),
				},
				{
					Method:  http.MethodPost,
					Path:    "/influencer-gmv-summary",
					Handler: analysis.CreateInfluencerGmvSummaryHandler(serverCtx),
				},
				{
					Method:  http.MethodPut,
					Path:    "/influencer-gmv-summary",
					Handler: analysis.UpdateInfluencerGmvSummaryHandler(serverCtx),
				},
				{
					Method:  http.MethodGet,
					Path:    "/influencer-video-daily-metrics",
					Handler: analysis.GetInfluencerVideoDailyMetricsHandler(serverCtx),
				},
				{
					Method:  http.MethodPost,
					Path:    "/influencer-video-datasources",
					Handler: analysis.CreateInfluencerVideoDatasourceHandler(serverCtx),
				},
				{
					Method:  http.MethodGet,
					Path:    "/influencer-video-datasources/:id",
					Handler: analysis.GetInfluencerVideoDatasourceHandler(serverCtx),
				},
				{
					Method:  http.MethodGet,
					Path:    "/influencer-video-datasources",
					Handler: analysis.GetInfluencerVideoDatasourcesHandler(serverCtx),
				},
				{
					Method:  http.MethodPut,
					Path:    "/influencer-video-datasources/:id",
					Handler: analysis.UpdateInfluencerVideoDatasourceHandler(serverCtx),
				},
				{
					Method:  http.MethodDelete,
					Path:    "/influencer-video-datasources/:id",
					Handler: analysis.DeleteInfluencerVideoDatasourceHandler(serverCtx),
				},
				{
					Method:  http.MethodPost,
					Path:    "/influencer-product-references",
					Handler: analysis.CreateInfluencerProductReferenceHandler(serverCtx),
				},
				{
					Method:  http.MethodGet,
					Path:    "/influencer-product-references/:sku",
					Handler: analysis.GetInfluencerProductReferenceHandler(serverCtx),
				},
				{
					Method:  http.MethodGet,
					Path:    "/influencer-product-references",
					Handler: analysis.GetInfluencerProductReferencesHandler(serverCtx),
				},
				{
					Method:  http.MethodPut,
					Path:    "/influencer-product-references/:sku",
					Handler: analysis.UpdateInfluencerProductReferenceHandler(serverCtx),
				},
				{
					Method:  http.MethodDelete,
					Path:    "/influencer-product-references/:sku",
					Handler: analysis.DeleteInfluencerProductReferenceHandler(serverCtx),
				},
				{
					Method:  http.MethodPost,
					Path:    "/influencer-product-references/batch",
					Handler: analysis.BatchCreateInfluencerProductReferenceHandler(serverCtx),
				},
				{
					Method:  http.MethodPost,
					Path:    "/live-performances",
					Handler: analysis.CreateLivePerformanceHandler(serverCtx),
				},
				{
					Method:  http.MethodGet,
					Path:    "/live-performances/:id",
					Handler: analysis.GetLivePerformanceHandler(serverCtx),
				},
				{
					Method:  http.MethodGet,
					Path:    "/live-performances",
					Handler: analysis.GetLivePerformancesHandler(serverCtx),
				},
				{
					Method:  http.MethodPut,
					Path:    "/live-performances/:id",
					Handler: analysis.UpdateLivePerformanceHandler(serverCtx),
				},
				{
					Method:  http.MethodPut,
					Path:    "/live-performances/sync",
					Handler: analysis.SyncLivePerformanceHandler(serverCtx),
				},
				{
					Method:  http.MethodDelete,
					Path:    "/live-performances/:id",
					Handler: analysis.DeleteLivePerformanceHandler(serverCtx),
				},
				{
					Method:  http.MethodPost,
					Path:    "/live-performance-datasources",
					Handler: analysis.CreateLivePerformanceDatasourceHandler(serverCtx),
				},
				{
					Method:  http.MethodGet,
					Path:    "/live-performance-datasources/:id",
					Handler: analysis.GetLivePerformanceDatasourceHandler(serverCtx),
				},
				{
					Method:  http.MethodGet,
					Path:    "/live-performance-datasources",
					Handler: analysis.GetLivePerformanceDatasourcesHandler(serverCtx),
				},
				{
					Method:  http.MethodPut,
					Path:    "/live-performance-datasources/:id",
					Handler: analysis.UpdateLivePerformanceDatasourceHandler(serverCtx),
				},
				{
					Method:  http.MethodDelete,
					Path:    "/live-performance-datasources/:id",
					Handler: analysis.DeleteLivePerformanceDatasourceHandler(serverCtx),
				},
				{
					Method:  http.MethodPost,
					Path:    "/live-performance-goals",
					Handler: analysis.CreateLivePerformanceGoalHandler(serverCtx),
				},
				{
					Method:  http.MethodGet,
					Path:    "/live-performance-goals/:month",
					Handler: analysis.GetLivePerformanceGoalHandler(serverCtx),
				},
				{
					Method:  http.MethodGet,
					Path:    "/live-performance-goals",
					Handler: analysis.GetLivePerformanceGoalsHandler(serverCtx),
				},
				{
					Method:  http.MethodPut,
					Path:    "/live-performance-goals/:month",
					Handler: analysis.UpdateLivePerformanceGoalHandler(serverCtx),
				},
				{
					Method:  http.MethodDelete,
					Path:    "/live-performance-goals/:month",
					Handler: analysis.DeleteLivePerformanceGoalHandler(serverCtx),
				},
			}...,
		),
		rest.WithPrefix("/api/analysis"),
	)

	server.AddRoutes(
		rest.WithMiddlewares(
			[]rest.Middleware{serverCtx.AuthInterceptor},
			[]rest.Route{
				{
					Method:  http.MethodGet,
					Path:    "/repositories",
					Handler: release.GetRepositoriesHandler(serverCtx),
				},
				{
					Method:  http.MethodGet,
					Path:    "/repositories/:id",
					Handler: release.GetRepositoryHandler(serverCtx),
				},
				{
					Method:  http.MethodGet,
					Path:    "/repositories/:id/branches",
					Handler: release.GetRepositoryBranchesHandler(serverCtx),
				},
				{
					Method:  http.MethodGet,
					Path:    "/jenkins/jobs",
					Handler: release.GetJenkinsJobListHandler(serverCtx),
				},
				{
					Method:  http.MethodPost,
					Path:    "/jenkins/jobs/:jobFullPath/build",
					Handler: release.BuildJenkinsJobHandler(serverCtx),
				},
				{
					Method:  http.MethodGet,
					Path:    "/applications",
					Handler: release.GetApplicationsHandler(serverCtx),
				},
				{
					Method:  http.MethodPost,
					Path:    "/applications",
					Handler: release.CreateApplicationHandler(serverCtx),
				},
				{
					Method:  http.MethodGet,
					Path:    "/applications/:id",
					Handler: release.GetApplicationHandler(serverCtx),
				},
				{
					Method:  http.MethodPut,
					Path:    "/applications/:id",
					Handler: release.UpdateApplicationHandler(serverCtx),
				},
				{
					Method:  http.MethodDelete,
					Path:    "/applications/:id",
					Handler: release.DeleteApplicationHandler(serverCtx),
				},
				{
					Method:  http.MethodGet,
					Path:    "/applications/:id/config-file-content",
					Handler: release.GetApplicationConfigFileContentHandler(serverCtx),
				},
				{
					Method:  http.MethodGet,
					Path:    "/applications/:id/configmap-content",
					Handler: release.GetApplicationConfigMapContentHandler(serverCtx),
				},
				{
					Method:  http.MethodGet,
					Path:    "/k8s/configmaps",
					Handler: release.GetConfigMapsHandler(serverCtx),
				},
				{
					Method:  http.MethodGet,
					Path:    "/k8s/configmaps/content",
					Handler: release.GetConfigMapContentHandler(serverCtx),
				},
				{
					Method:  http.MethodPost,
					Path:    "/ai-config-compare",
					Handler: release.AiConfigCompareHandler(serverCtx),
				},
				{
					Method:  http.MethodPost,
					Path:    "/plans",
					Handler: release.CreateReleasePlanHandler(serverCtx),
				},
				{
					Method:  http.MethodGet,
					Path:    "/plans",
					Handler: release.GetReleasePlansHandler(serverCtx),
				},
				{
					Method:  http.MethodGet,
					Path:    "/plans/:plan_id",
					Handler: release.GetReleasePlanHandler(serverCtx),
				},
				{
					Method:  http.MethodPatch,
					Path:    "/plans/:plan_id/status",
					Handler: release.UpdateReleasePlanStatusHandler(serverCtx),
				},
				{
					Method:  http.MethodPost,
					Path:    "/plans/:plan_id/approvals",
					Handler: release.CreateReleasePlanApprovalHandler(serverCtx),
				},
				{
					Method:  http.MethodPost,
					Path:    "/application-tasks/:task_id/exec",
					Handler: release.ExecuteApplicationTaskHandler(serverCtx),
				},
				{
					Method:  http.MethodPost,
					Path:    "/db-migration-tasks/:task_id/exec",
					Handler: release.ExecuteDbMigrationTaskHandler(serverCtx),
				},
				{
					Method:  http.MethodPost,
					Path:    "/plans/:plan_id/tasks/exec",
					Handler: release.ExecuteAllTasksHandler(serverCtx),
				},
				{
					Method:  http.MethodGet,
					Path:    "/application-tasks/:task_id",
					Handler: release.GetApplicationTaskHandler(serverCtx),
				},
				{
					Method:  http.MethodGet,
					Path:    "/plans/:plan_id/approvals",
					Handler: release.GetReleasePlanApprovalsHandler(serverCtx),
				},
				{
					Method:  http.MethodGet,
					Path:    "/plans/:plan_id/approvals/:approval_id",
					Handler: release.GetReleasePlanApprovalHandler(serverCtx),
				},
			}...,
		),
		rest.WithPrefix("/api/release"),
	)

	server.AddRoutes(
		rest.WithMiddlewares(
			[]rest.Middleware{serverCtx.AuthInterceptor},
			[]rest.Route{
				{
					Method:  http.MethodGet,
					Path:    "/config",
					Handler: acl.GetAclConfigHandler(serverCtx),
				},
				{
					Method:  http.MethodPut,
					Path:    "/config",
					Handler: acl.UpdateAclConfigHandler(serverCtx),
				},
				{
					Method:  http.MethodPost,
					Path:    "/check-permission",
					Handler: acl.CheckPermissionHandler(serverCtx),
				},
				{
					Method:  http.MethodGet,
					Path:    "/user-permissions",
					Handler: acl.GetUserPermissionsHandler(serverCtx),
				},
				{
					Method:  http.MethodGet,
					Path:    "/users-roles",
					Handler: acl.GetUsersRolesHandler(serverCtx),
				},
				{
					Method:  http.MethodGet,
					Path:    "/available-paths",
					Handler: acl.GetAvailablePathsHandler(serverCtx),
				},
				{
					Method:  http.MethodGet,
					Path:    "/roles",
					Handler: acl.GetRolesHandler(serverCtx),
				},
				{
					Method:  http.MethodPost,
					Path:    "/roles",
					Handler: acl.CreateRoleHandler(serverCtx),
				},
				{
					Method:  http.MethodPut,
					Path:    "/roles/:name",
					Handler: acl.UpdateRoleHandler(serverCtx),
				},
				{
					Method:  http.MethodDelete,
					Path:    "/roles/:name",
					Handler: acl.DeleteRoleHandler(serverCtx),
				},
				{
					Method:  http.MethodPost,
					Path:    "/assign-roles",
					Handler: acl.AssignUserRolesHandler(serverCtx),
				},
				{
					Method:  http.MethodGet,
					Path:    "/users",
					Handler: acl.GetUsersHandler(serverCtx),
				},
				{
					Method:  http.MethodPost,
					Path:    "/users",
					Handler: acl.CreateUserHandler(serverCtx),
				},
				{
					Method:  http.MethodPost,
					Path:    "/users/:id/disable",
					Handler: acl.DisableUserHandler(serverCtx),
				},
				{
					Method:  http.MethodPost,
					Path:    "/users/:id/password",
					Handler: acl.ResetPasswordHandler(serverCtx),
				},
				{
					Method:  http.MethodPost,
					Path:    "/users/:id/enable",
					Handler: acl.EnableUserHandler(serverCtx),
				},
			}...,
		),
		rest.WithPrefix("/api/acl"),
	)

	server.AddRoutes(
		rest.WithMiddlewares(
			[]rest.Middleware{serverCtx.AuthInterceptor},
			[]rest.Route{
				{
					Method:  http.MethodPost,
					Path:    "/onduty/pairs",
					Handler: team.AddOndutyPairHandler(serverCtx),
				},
				{
					Method:  http.MethodGet,
					Path:    "/onduty/current",
					Handler: team.GetCurrentOndutyHandler(serverCtx),
				},
				{
					Method:  http.MethodGet,
					Path:    "/onduty/monthly",
					Handler: team.GetMonthlyOndutyHandler(serverCtx),
				},
				{
					Method:  http.MethodGet,
					Path:    "/onduty/pairs",
					Handler: team.ListOndutyPairsHandler(serverCtx),
				},
				{
					Method:  http.MethodPut,
					Path:    "/onduty/pairs/order",
					Handler: team.UpdateOndutyPairOrderHandler(serverCtx),
				},
				{
					Method:  http.MethodDelete,
					Path:    "/onduty/pairs",
					Handler: team.DeleteOndutyPairHandler(serverCtx),
				},
				{
					Method:  http.MethodGet,
					Path:    "/onduty/schedule-by-email",
					Handler: team.GetOndutyScheduleByEmailHandler(serverCtx),
				},
			}...,
		),
		rest.WithPrefix("/api/team"),
	)

	server.AddRoutes(
		rest.WithMiddlewares(
			[]rest.Middleware{serverCtx.AuthInterceptor},
			[]rest.Route{
				{
					Method:  http.MethodPost,
					Path:    "/flows",
					Handler: approval.CreateApprovalFlowHandler(serverCtx),
				},
				{
					Method:  http.MethodGet,
					Path:    "/flows",
					Handler: approval.GetApprovalFlowsHandler(serverCtx),
				},
				{
					Method:  http.MethodGet,
					Path:    "/flows/:id",
					Handler: approval.GetApprovalFlowHandler(serverCtx),
				},
				{
					Method:  http.MethodPut,
					Path:    "/flows/:id",
					Handler: approval.UpdateApprovalFlowHandler(serverCtx),
				},
				{
					Method:  http.MethodDelete,
					Path:    "/flows/:id",
					Handler: approval.DeleteApprovalFlowHandler(serverCtx),
				},
				{
					Method:  http.MethodGet,
					Path:    "/tasks/me",
					Handler: approval.GetMyApprovalTasksHandler(serverCtx),
				},
				{
					Method:  http.MethodGet,
					Path:    "/tasks",
					Handler: approval.GetAllApprovalTasksHandler(serverCtx),
				},
				{
					Method:  http.MethodGet,
					Path:    "/tasks/:id",
					Handler: approval.GetApprovalTaskHandler(serverCtx),
				},
				{
					Method:  http.MethodPost,
					Path:    "/tasks/:task_id/approve",
					Handler: approval.ApproveTaskHandler(serverCtx),
				},
				{
					Method:  http.MethodPost,
					Path:    "/tasks/:task_id/reject",
					Handler: approval.RejectTaskHandler(serverCtx),
				},
				{
					Method:  http.MethodPost,
					Path:    "/tasks/:task_id/skip",
					Handler: approval.SkipTaskHandler(serverCtx),
				},
				{
					Method:  http.MethodGet,
					Path:    "/instances",
					Handler: approval.GetApprovalInstancesHandler(serverCtx),
				},
				{
					Method:  http.MethodGet,
					Path:    "/instances/:id",
					Handler: approval.GetApprovalInstanceHandler(serverCtx),
				},
				{
					Method:  http.MethodGet,
					Path:    "/instances/:id/tasks",
					Handler: approval.GetApprovalInstanceTasksHandler(serverCtx),
				},
			}...,
		),
		rest.WithPrefix("/api/approval"),
	)

	server.AddRoutes(
		rest.WithMiddlewares(
			[]rest.Middleware{serverCtx.AuthInterceptor},
			[]rest.Route{
				{
					Method:  http.MethodGet,
					Path:    "/xxl/executors",
					Handler: ops.ListXxlExecutorsHandler(serverCtx),
				},
				{
					Method:  http.MethodPost,
					Path:    "/xxl/executors/:id/copy-jobs-from-indonesia",
					Handler: ops.CopyJobsFromIndonesiaHandler(serverCtx),
				},
				{
					Method:  http.MethodPost,
					Path:    "/dms/query",
					Handler: ops.ExecuteDMSQueryHandler(serverCtx),
				},
				{
					Method:  http.MethodPost,
					Path:    "/dms/mutation",
					Handler: ops.ExecuteDMSMutationHandler(serverCtx),
				},
				{
					Method:  http.MethodGet,
					Path:    "/oss/presigned-url",
					Handler: ops.GetPresignedUrlHandler(serverCtx),
				},
				{
					Method:  http.MethodGet,
					Path:    "/oss/ohsome-apk-versions",
					Handler: ops.GetOhsomeApkVersionsHandler(serverCtx),
				},
				{
					Method:  http.MethodGet,
					Path:    "/o2o/grab/config/list",
					Handler: ops.GetO2OGrabConfigListHandler(serverCtx),
				},
				{
					Method:  http.MethodPost,
					Path:    "/o2o/grab/config/add",
					Handler: ops.AddO2OGrabConfigHandler(serverCtx),
				},
				{
					Method:  http.MethodPost,
					Path:    "/o2o/grab/config/update",
					Handler: ops.UpdateO2OGrabConfigHandler(serverCtx),
				},
				{
					Method:  http.MethodPost,
					Path:    "/o2o/grab/config/delete",
					Handler: ops.DeleteO2OGrabConfigHandler(serverCtx),
				},
				{
					Method:  http.MethodPost,
					Path:    "/image/remove-logo",
					Handler: ops.RemoveLogoFromImageHandler(serverCtx),
				},
				{
					Method:  http.MethodPost,
					Path:    "/image/remove-logo-v2",
					Handler: ops.RemoveLogoFromImageV2Handler(serverCtx),
				},
				{
					Method:  http.MethodGet,
					Path:    "/rocketmq/topics",
					Handler: ops.ListRocketMQTopicsHandler(serverCtx),
				},
				{
					Method:  http.MethodGet,
					Path:    "/rocketmq/consumer-groups",
					Handler: ops.ListRocketMQConsumerGroupsHandler(serverCtx),
				},
				{
					Method:  http.MethodPost,
					Path:    "/rocketmq/topics",
					Handler: ops.CreateRocketMQTopicHandler(serverCtx),
				},
				{
					Method:  http.MethodPost,
					Path:    "/rocketmq/consumer-groups",
					Handler: ops.CreateRocketMQConsumerGroupHandler(serverCtx),
				},
				{
					Method:  http.MethodDelete,
					Path:    "/rocketmq/topics/:topicName",
					Handler: ops.DeleteRocketMQTopicHandler(serverCtx),
				},
				{
					Method:  http.MethodDelete,
					Path:    "/rocketmq/consumer-groups/:consumerGroupId",
					Handler: ops.DeleteRocketMQConsumerGroupHandler(serverCtx),
				},
				{
					Method:  http.MethodGet,
					Path:    "/feature-control/config",
					Handler: ops.GetFeatureControlConfigHandler(serverCtx),
				},
				{
					Method:  http.MethodPost,
					Path:    "/feature-control/config",
					Handler: ops.UpdateFeatureControlConfigHandler(serverCtx),
				},
			}...,
		),
		rest.WithPrefix("/api/ops"),
	)

	server.AddRoutes(
		rest.WithMiddlewares(
			[]rest.Middleware{serverCtx.AuthInterceptor},
			[]rest.Route{
				{
					Method:  http.MethodGet,
					Path:    "/funifun/reward-items",
					Handler: ohsome.ListFunifunRewardItemsHandler(serverCtx),
				},
				{
					Method:  http.MethodPost,
					Path:    "/regions/:region/funifun/reward-items/:id/sync/:targetRegion",
					Handler: ohsome.SyncFunifunRewardItemsHandler(serverCtx),
				},
			}...,
		),
		rest.WithPrefix("/api/ohsome"),
	)
}
