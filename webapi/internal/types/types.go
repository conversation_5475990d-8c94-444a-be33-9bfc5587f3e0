// Code generated by goctl. DO NOT EDIT.
// goctl 1.8.4

package types

type LoginReq struct {
	Username string `json:"username"`
	Password string `json:"password"`
}

type LoginResp struct {
	Id    string `json:"id"`
	Name  string `json:"name"`
	Token string `json:"token"`
}

type OAuthConsentReq struct {
	AppKey string `path:"app_key"`
}

type OAuthRedirectReq struct {
	AppKey string `path:"app_key"`
	Code   string `form:"code"`
	ShopId string `form:"shop_id,optional"`
}

type GetUserInfoReq struct {
	Token string `json:"token"`
}

type GetUserInfoResp struct {
	Id   string `json:"id"`
	Name string `json:"name"`
}

type FulfillmentPreference struct {
	Key                string `json:"key"`
	Region             string `json:"region"`
	Channel            string `json:"channel"`
	WarehouseCode      string `json:"warehouseCode"`
	DisableFulfillment string `json:"disableFulfillment"`
}

type GetFulfillmentPreferencesReq struct {
	Region        string `form:"region,optional"`
	Channel       string `form:"channel,optional"`
	WarehouseCode string `form:"warehouseCode,optional"`
}

type GetFulfillmentPreferencesResp struct {
	Data []FulfillmentPreference `json:"data"`
}

type UpdateFulfillmentPreferencesReq struct {
	Region             string `json:"region"`
	Channel            string `json:"channel"`
	WarehouseCode      string `json:"warehouseCode"`
	DisableFulfillment string `json:"disableFulfillment"`
}

type RedisQueueMetrics struct {
	Region      string `json:"region"`
	Channel     string `json:"channel"`
	QueueType   string `json:"queueType"`
	Queue       string `json:"queue"`
	QueueLength int64  `json:"queueLength"`
}

type GetRedisQueueMetricsReq struct {
	Region    string `form:"region,optional"`
	Channel   string `form:"channel,optional"`
	QueueType string `form:"queueType,optional"`
	Queue     string `form:"queue,optional"`
}

type GetRedisQueueMetricsResp struct {
	Data []RedisQueueMetrics `json:"data"`
}

type GetOrdersMetricsReq struct {
	Region string `form:"region,optional"`
	ShopId int64  `form:"shopId,optional"`
	Date   string `form:"date,optional"`
}

type GetOrdersMetricsResp struct {
	Data []OrdersMetrics `json:"data"`
}

type OrdersMetrics struct {
	Region   string `json:"region"`
	ShopId   int64  `json:"shopId"`
	ShopName string `json:"shopName"`
	Channel  string `json:"channel"`
	Date     string `json:"date"`
	Count    int64  `json:"count"`
}

type User struct {
	Id       int64  `json:"id"`
	Region   string `json:"region"`
	Username string `json:"username"`
	Enabled  int64  `json:"enabled"`
}

type GetUsersReq struct {
	Region   string `form:"region,optional"`
	Username string `form:"username,optional"`
	Enabled  int64  `form:"enabled,optional"`
}

type GetUsersResp struct {
	Data []User `json:"data"`
}

type CreateUserReq struct {
	Region   string `json:"region"`
	Username string `json:"username"`
	Password string `json:"password"`
}

type CreateUserResp struct {
	Region   string `json:"region"`
	Username string `json:"username"`
}

type DisableUserReq struct {
	Id     int64  `path:"id"`
	Region string `json:"region"`
}

type EnableUserReq struct {
	Id     int64  `path:"id"`
	Region string `json:"region"`
}

type DeleteQueueItemReq struct {
	Region       string `json:"region"`
	Channel      string `path:"channel"`
	ThirdOrderId string `json:"thirdOrderId"`
}

type DeleteQueueItemResp struct {
	Region       string `json:"region"`
	Queue        string `json:"queue"`
	Channel      string `json:"channel"`
	ThirdOrderId string `json:"thirdOrderId"`
}

type GetRedisQueuesReq struct {
	Region       string `form:"region,optional"`
	Queue        string `form:"queue,optional"`
	Channel      string `form:"channel,optional"`
	ThirdOrderId string `form:"thirdOrderId,optional"`
}

type RedisQueue struct {
	Region       string `json:"region"`
	Queue        string `json:"queue"`
	Channel      string `json:"channel"`
	ShopKey      string `json:"shopKey"`
	ThirdOrderId string `json:"thirdOrderId"`
	OrderState   string `json:"orderState"`
}

type GetRedisQueuesResp struct {
	Data []RedisQueue `json:"data"`
}

type PasswordRestReq struct {
	Id       int64  `path:"id"`
	Region   string `json:"region"`
	Password string `json:"password"`
}

type Shop struct {
	Id                 int64  `json:"id,optional"`
	Region             string `json:"region"`
	Platform           string `json:"platform"`
	ShopKey            string `json:"shopKey"`
	ShopName           string `json:"shopName"`
	PlatformShopID     string `json:"platformShopId"`
	Currency           string `json:"currency"`
	AppKey             string `json:"appKey,optional"`
	AppSecret          string `json:"appSecret,optional"`
	AccessToken        string `json:"accessToken,optional"`
	RefreshToken       string `json:"refreshToken,optional"`
	ShopCipher         string `json:"shopCipher,optional"`
	Pro                bool   `json:"pro,optional"`
	MultiWarehouseFlag int    `json:"multiWarehouseFlag,optional"`
	Status             string `json:"status,optional"`
}

type CreateShopReq struct {
	Shop
}

type SyncShopReq struct {
	Id                 int64  `path:"id"`
	Region             string `json:"region"`
	Platform           string `json:"platform"`
	ShopKey            string `json:"shopKey"`
	ShopName           string `json:"shopName"`
	PlatformShopID     string `json:"platformShopId"`
	Currency           string `json:"currency"`
	AppKey             string `json:"appKey,optional"`
	AppSecret          string `json:"appSecret,optional"`
	AccessToken        string `json:"accessToken,optional"`
	RefreshToken       string `json:"refreshToken,optional"`
	ShopCipher         string `json:"shopCipher,optional"`
	Pro                bool   `json:"pro,optional"`
	MultiWarehouseFlag int    `json:"multiWarehouseFlag,optional"`
}

type GetShopsReq struct {
	Region         string `form:"region,optional"`
	ShopKey        string `form:"shopKey,optional"`
	ShopName       string `form:"shopName,optional"`
	Platform       string `form:"platform,optional"`
	PlatformShopID string `form:"platformShopId,optional"`
}

type UpdateShopReq struct {
	Id                 int64  `path:"id"`
	ShopName           string `json:"shopName"`
	AppKey             string `json:"appKey"`
	Region             string `json:"region"`
	MultiWarehouseFlag int    `json:"multiWarehouseFlag"`
}

type GetShopsResp struct {
	Data []Shop `json:"data"`
}

type GetThirdOrderReq struct {
	Id      string `path:"id"`
	Region  string `form:"region"`
	ShopKey string `form:"shopKey"`
}

type ThirdOrderDetailResp struct {
	RawData string `json:"rawData"`
}

type GetThirdStocksReq struct {
	Region  string `form:"region"`
	ShopKey string `form:"shopKey"`
	Skus    string `form:"skus"`
}

type ThirdStockItem struct {
	Sku              string `json:"sku"`
	Channel          string `json:"channel"`
	ChannelProductId string `json:"channelProductId"`
	Quantity         int64  `json:"quantity"`
	RawData          string `json:"rawData"`
}

type ThirdStocksResp struct {
	Data []ThirdStockItem `json:"data"`
}

type GetShopAuthorizationsReq struct {
	Region   string `form:"region,optional"`
	Platform string `form:"platform,optional"`
	ShopKey  string `form:"shopKey,optional"`
}

type ShopAuthorization struct {
	Region         string `json:"region"`
	Platform       string `json:"platform"`
	ShopKey        string `json:"shopKey"`
	ShopName       string `json:"shopName"`
	PlatformShopID string `json:"platformShopId"`
	AccessToken    string `json:"accessToken"`
	RefreshToken   string `json:"refreshToken"`
	ShopCipher     string `json:"shopCipher,optional"`
}

type GetShopAuthorizationsResp struct {
	Data []ShopAuthorization `json:"data"`
}

type CreateShopAuthorizationsReq struct {
	ShopAuthorization
}

type CreateShopAuthorizationsResp struct {
	ShopAuthorization
}

type GetShopAppsReq struct {
	Region   string `form:"region,optional"`
	Platform string `form:"platform,optional"`
}

type ShopApp struct {
	Region       string            `json:"region"`
	Currency     string            `json:"currency"`
	Platform     string            `json:"platform"`
	AppKey       string            `json:"appKey"`
	AppSecret    string            `json:"appSecret"`
	IsSandbox    bool              `json:"isSandbox"`
	InstallUrls  map[string]string `json:"installUrls"`
	RedirectUrls map[string]string `json:"redirectUrls"`
}

type GetShopAppsResp struct {
	Data []ShopApp `json:"data"`
}

type TourMigrationsListRequest struct {
	Region string `form:"region,optional"`
}

type TourMigrationsResponse struct {
	Region string `json:"region"`
	Status string `json:"status"`
}

type TourMigrationsListResponse struct {
	Data []TourMigrationsResponse `json:"data"`
}

type TourMigrationsUpdateRequest struct {
	Region string `path:"region"`
	Status string `json:"status"`
}

type GetScmInventoryItemsReq struct {
	Skus            string `form:"skus,optional"`
	ProjectCodeList string `form:"projectCodeList,optional"`
	Page            int    `form:"page,optional"`
	PageSize        int    `form:"pageSize,optional"`
	UpdatedAtMin    string `form:"updatedAtMin,optional"`
	UpdatedAtMax    string `form:"updatedAtMax,optional"`
}

type GetScmInventoryItemsResp struct {
	RawData string `json:"rawData"`
}

type GetScmStocksReq struct {
	Sku           string `form:"skus,optional"`
	WarehouseCode string `form:"warehouseCode,optional"`
	Page          int    `form:"page,optional"`
	PageSize      int    `form:"pageSize,optional"`
}

type GetScmStocksResp struct {
	RawData string `json:"rawData"`
}

type GetScmRequestAuditLogsReq struct {
	Region  string `form:"region"`
	Keyword string `form:"keyword,optional"`
}

type ScmRequestAuditLog struct {
	Id               int64  `json:"id"`
	RequestURL       string `json:"requestUrl"`
	RequestMethod    string `json:"requestMethod"`
	RequestPayload   string `json:"requestPayload"`
	CreatedAt        string `json:"createdAt"`
	ConvertedPayload string `json:"convertedPayload"`
	ResponsePayload  string `json:"responsePayload"`
}

type GetScmRequestAuditLogsResp struct {
	Data []ScmRequestAuditLog `json:"data"`
}

type GetWarehousesReq struct {
	WarehouseCode         string `form:"warehouseCode,optional"`
	Region                string `form:"region,optional"`
	PhysicalWarehouseCode string `form:"physicalWarehouseCode,optional"`
	LogicalWarehouseCode  string `form:"logicalWarehouseCode,optional"`
}

type GetWarehousesResp struct {
	Data []Warehouse `json:"data"`
}

type Warehouse struct {
	Id                    int64  `json:"id"`
	Region                string `json:"region"`
	WarehouseCode         string `json:"warehouseCode"`
	Name                  string `json:"name"`
	ThirdCode             string `json:"thirdCode"`
	ActiveFlag            int64  `json:"activeFlag"`
	CreatedAt             string `json:"createdAt"`
	CreatedBy             int64  `json:"createdBy"`
	UpdatedAt             string `json:"updatedAt"`
	UpdatedBy             int64  `json:"updatedBy"`
	FlagDeleted           int64  `json:"flagDeleted"`
	DeletedBy             int64  `json:"deletedBy"`
	DeletedAt             string `json:"deletedAt"`
	DefaultFlag           int64  `json:"defaultFlag"`
	PhysicalWarehouseCode string `json:"physicalWarehouseCode,optional"`
	LogicalWarehouseCode  string `json:"logicalWarehouseCode,optional"`
}

type UpdateWarehouseReq struct {
	Id                    int64  `path:"id"`
	Region                string `json:"region"`
	Name                  string `json:"name,optional"`
	ThirdCode             string `json:"thirdCode,optional"`
	PhysicalWarehouseCode string `json:"physicalWarehouseCode,optional"`
	LogicalWarehouseCode  string `json:"logicalWarehouseCode,optional"`
}

type CreateWarehouseReq struct {
	Region                string `json:"region"`
	WarehouseCode         string `json:"warehouseCode"`
	Name                  string `json:"name"`
	ThirdCode             string `json:"thirdCode,optional"`
	ActiveFlag            int64  `json:"activeFlag,optional"`
	DefaultFlag           int64  `json:"defaultFlag,optional"`
	PhysicalWarehouseCode string `json:"physicalWarehouseCode"`
	LogicalWarehouseCode  string `json:"logicalWarehouseCode"`
}

type CreateWarehouseResp struct {
	Success bool   `json:"success"`
	Error   string `json:"errorMessage,optional"`
}

type AddShopAppsReq struct {
	ShopApp
}

type AddShopAppsResp struct {
	ShopApp
}

type PushOrderToScmReq struct {
	Region        string `json:"region"`
	OrderId       string `json:"orderId"`
	Authorization string `header:"authorization"`
}

type PushOrderToScmResp struct {
	Region  string `json:"region"`
	OrderId string `json:"orderId"`
	Success bool   `json:"success"`
	Error   string `json:"error"`
}

type PushOrderToScmWithoutCancelReq struct {
	Region        string `json:"region"`
	OrderId       string `json:"orderId"`
	Authorization string `header:"authorization"`
}

type PushOrderToScmWithoutCancelResp struct {
	Region  string `json:"region"`
	OrderId string `json:"orderId"`
	Success bool   `json:"success"`
	Error   string `json:"error"`
}

type GetWmsInboundOrdersReq struct {
	WarehouseCode string   `json:"warehouseCode"`
	OrderIds      []string `json:"orderIds"`
}

type GetWmsOutboundOrdersReq struct {
	WarehouseCode string   `json:"warehouseCode"`
	OrderIds      []string `json:"orderIds"`
}

type GetWmsOrdersResp struct {
	RawData string `json:"rawData"`
}

type GetOrderIdsReq struct {
	Region    string `form:"region"`
	ShopId    int64  `form:"shopId,optional"`
	StartDate string `form:"startDate"`
	EndDate   string `form:"endDate"`
	State     string `form:"state,optional"`
}

type GetOrderIdsResp struct {
	Data []string `json:"data"`
}

type GetOrdersReq struct {
	Region    string `form:"region"`
	ShopId    int64  `form:"shopId,optional"`
	Ids       string `form:"ids,optional"`
	StartDate string `form:"startDate"`
	EndDate   string `form:"endDate"`
	State     string `form:"state,optional"`
}

type GetOrdersResp struct {
	Data []OrderBase `json:"data"`
}

type OrderBase struct {
	ID               string `json:"id"`
	ChannelOrderID   string `json:"channelOrderId"`
	State            string `json:"state"`
	DisplayState     string `json:"displayState"`
	StorePlatform    string `json:"storePlatform"`
	ShopID           int16  `json:"shopId"`
	StoreState       string `json:"storeState"`
	WarehouseID      string `json:"warehouseId"`
	ExpressID        string `json:"expressId"`
	WarehouseState   string `json:"warehouseState"`
	OrderTime        string `json:"orderTime"`
	PayTime          string `json:"payTime"`
	PayWay           string `json:"payWay"`
	OutOfStockReason string `json:"outOfStockReason"`
	ExceptionReason  string `json:"exceptionReason"`
	PreOrder         bool   `json:"preOrder"`
	DeliverFirst     bool   `json:"deliverFirst"`
	Remark           bool   `json:"remark"`
	Hold             bool   `json:"hold"`
	Exception        bool   `json:"exception"`
	Oversold         bool   `json:"oversold"`
	UpdateTime       string `json:"updateTime"`
	WarehouseCode    string `json:"warehouseCode"`
	CustomAttributes string `json:"customAttributes"`
}

type GetOrderDeliveryCodesReq struct {
	Region    string `form:"region"`
	ShopId    int64  `form:"shopId,optional"`
	StartDate string `form:"startDate"`
	EndDate   string `form:"endDate"`
}

type GetOrderDeliveryCodesResp struct {
	Data []string `json:"data"`
}

type GetReturnOrderIdsReq struct {
	Region    string `form:"region"`
	ShopId    int64  `form:"shopId,optional"`
	StartDate string `form:"startDate"`
	EndDate   string `form:"endDate"`
}

type GetReturnOrderIdsResp struct {
	Data []string `json:"data"`
}

type OrderStateMetrics struct {
	State      string `json:"state"`
	OrderTotal int64  `json:"orderTotal"`
}

type GetOrderStateMetricsReq struct {
	Region    string `form:"region"`
	ShopId    int64  `form:"shopId,optional"`
	StartDate string `form:"startDate"`
	EndDate   string `form:"endDate"`
}

type GetOrderStateMetricsResp struct {
	Data []OrderStateMetrics `json:"data"`
}

type OrderShopStateMetrics struct {
	ShopId     int64  `json:"shopId"`
	ShopName   string `json:"shopName"`
	State      string `json:"state"`
	OrderTotal int64  `json:"orderTotal"`
}

type GetOrderShopStateMetricsReq struct {
	Region    string `form:"region"`
	StartDate string `form:"startDate"`
	EndDate   string `form:"endDate"`
}

type GetOrderShopStateMetricsResp struct {
	Data []OrderShopStateMetrics `json:"data"`
}

type GetShopReq struct {
	Id int64 `path:"id"`
}

type GetShopResp struct {
	Data Shop `json:"data"`
}

type GetProductImageUrlsReq struct {
	Skus []string `json:"skus"`
}

type ProductImageUrl struct {
	Barcode string `json:"barcode"`
	Sku     string `json:"sku"`
	Image1  string `json:"image_1"`
	Image2  string `json:"image_2"`
	Image3  string `json:"image_3"`
	Image4  string `json:"image_4"`
	Image5  string `json:"image_5"`
	Image6  string `json:"image_6"`
}

type GetProductImageUrlsResp struct {
	Data []ProductImageUrl `json:"data"`
}

type FixSkusImagesReq struct {
	Region string   `json:"region"`
	Skus   []string `json:"skus"`
}

type FixSkusImagesResp struct {
	Success bool   `json:"success"`
	Error   string `json:"error,optional"`
}

type GetOrdersWithoutTrackingReq struct {
	Region    string `form:"region"`
	StartDate string `form:"startDate"`
	EndDate   string `form:"endDate"`
}

type GetOrdersWithoutTrackingResp struct {
	OrderIds   []string `json:"orderIds"`
	OrderCount int64    `json:"orderCount"`
}

type GetOrdersWithoutDeliveryReq struct {
	Region    string `form:"region"`
	StartDate string `form:"startDate"`
	EndDate   string `form:"endDate"`
}

type GetOrdersWithoutDeliveryResp struct {
	OrderIds   []string `json:"orderIds"`
	OrderCount int64    `json:"orderCount"`
}

type GetOrdersWithStockIssueReq struct {
	Region    string `form:"region"`
	StartDate string `form:"startDate"`
	EndDate   string `form:"endDate"`
}

type GetOrdersWithStockIssueResp struct {
	OrderIds   []string `json:"orderIds"`
	OrderCount int64    `json:"orderCount"`
}

type GetOrdersWithCombineSkuIssueReq struct {
	Region    string `form:"region"`
	StartDate string `form:"startDate"`
	EndDate   string `form:"endDate"`
}

type GetOrdersWithCombineSkuIssueResp struct {
	OrderIds   []string `json:"orderIds"`
	OrderCount int64    `json:"orderCount"`
}

type OrderSyncMetrics struct {
	TotalOrders               []OrderBase `json:"totalOrders"`
	OrdersWithoutTracking     []OrderBase `json:"ordersWithoutTracking"`
	OrdersWithoutDelivery     []OrderBase `json:"ordersWithoutDelivery"`
	OrdersWithStockIssue      []OrderBase `json:"ordersWithStockIssue"`
	OrdersWithCombineSkuIssue []OrderBase `json:"ordersWithCombineSkuIssue"`
	WmsOrderTotal             []string    `json:"wmsOrderTotal"`
	IsSynchronized            bool        `json:"isSynchronized"`
	MissingOrders             []OrderBase `json:"missingOrders"`
	WmsDocuments              []OrderBase `json:"wmsDocuments"`
}

type GetOrderSyncMetricsReq struct {
	Region    string `form:"region"`
	StartDate string `form:"startDate"`
	EndDate   string `form:"endDate"`
}

type GetOrderSyncMetricsResp struct {
	Data OrderSyncMetrics `json:"data"`
}

type InitSearchTermsConfigReq struct {
	Region          string `json:"region"`
	WarehouseTermId string `json:"warehouseTermId"`
	WarehouseName   string `json:"warehouseName"`
}

type InitSearchTermsConfigResp struct {
	Success bool   `json:"success"`
	Error   string `json:"errorMessage,optional"`
}

type GetFinancialSalesEntityReq struct {
	PhysicalWarehouseCode string `form:"physical_warehouse_code"`
}

type GetFinancialSalesEntityResp struct {
	RawData string `json:"rawData"`
}

type GetFinancialSalesEntityByOrderIdsReq struct {
	Region   string   `json:"region"`
	OrderIds []string `json:"orderIds"`
}

type GetFinancialSalesEntityByOrderIdsResp struct {
	RawData string `json:"rawData"`
}

type InitShopProductReq struct {
	ShopId int64  `path:"id"`
	Region string `json:"region"`
}

type InitShopProductResp struct {
	Success bool   `json:"success"`
	Error   string `json:"error,optional"`
}

type SiteConfig struct {
	Region        string `json:"region"`        // 站点代码，如 TH
	RegionName    string `json:"regionName"`    // 站点名称，如 泰国
	Currency      string `json:"currency"`      // 货币代码，如 THB
	Warehouse     string `json:"warehouse"`     // 仓库代码，如 TLRT_TH_EC_PRO
	WarehouseName string `json:"warehouseName"` // 仓库名称，如 TLRT-泰国-电商-商品仓
	ProvinceId    string `json:"provinceId"`    // 省份ID，如 Jawa Barat
	CityId        string `json:"cityId"`        // 城市ID，如 Kabupaten Bekasi
	DetailId      string `json:"detailId"`      // 详细地址，如 WX4J+6Q7 Warehouse FMCG-A JD.ID Online shoping, Sagara Makmur, Kec. Tarumajaya, Kabupaten Bekasi, Jawa Barat 17211
}

type GetSiteConfigReq struct {
	Region string `form:"region,optional"` // 如果不提供，则返回所有站点配置
}

type GetSiteConfigResp struct {
	Data []SiteConfig `json:"data"`
}

type UpdateSiteConfigReq struct {
	Region        string `json:"region"`
	RegionName    string `json:"regionName"`
	Currency      string `json:"currency"`
	Warehouse     string `json:"warehouse"`
	WarehouseName string `json:"warehouseName"`
	ProvinceId    string `json:"provinceId"`
	CityId        string `json:"cityId"`
	DetailId      string `json:"detailId"`
}

type UpdateSiteConfigResp struct {
	Data SiteConfig `json:"data"`
}

type DeleteSiteConfigReq struct {
	Region string `path:"region"`
}

type DeleteSiteConfigResp struct {
	Success bool   `json:"success"`
	Error   string `json:"error,optional"`
}

type QueueConsumptionStatus struct {
	Region   string `json:"region"`
	Platform string `json:"platform"`
	Enabled  string `json:"enabled"`
	RedisKey string `json:"redisKey"`
}

type GetQueueConsumptionStatusReq struct {
	Region   string `form:"region,optional"`
	Platform string `form:"platform,optional"`
	Enabled  string `form:"enabled,optional"`
}

type GetQueueConsumptionStatusResp struct {
	Data []QueueConsumptionStatus `json:"data"`
}

type UpdateQueueConsumptionStatusReq struct {
	Region   string `json:"region"`
	Platform string `json:"platform"`
	Enabled  string `json:"enabled"`
}

type UpdateQueueConsumptionStatusResp struct {
	Success bool   `json:"success"`
	Error   string `json:"error,optional"`
}

type SyncOrderWarehouseStateReq struct {
	Region   string   `json:"region"`
	OrderIds []string `json:"orderIds"`
}

type SyncOrderWarehouseStateResp struct {
	Success bool   `json:"success"`
	Error   string `json:"error,optional"`
}

type SyncSingleOrderWarehouseStateReq struct {
	Region       string `path:"region"`
	DeliveryCode string `path:"deliveryCode"`
}

type SyncSingleOrderWarehouseStateResp struct {
	Success bool   `json:"success"`
	Error   string `json:"error,optional"`
}

type WarehouseStockSync struct {
	Id            int64  `json:"id"`
	Region        string `json:"region"`        // 站点代码，如 TH
	WarehouseCode string `json:"warehouseCode"` // 仓库编码
	NotifyRegion  string `json:"notifyRegion"`  // 通知地区
	CreateTime    string `json:"createTime"`    // 创建时间
	UpdateTime    string `json:"updateTime"`    // 更新时间
}

type GetWarehouseStockSyncsReq struct {
	Region        string `form:"region,optional"`
	WarehouseCode string `form:"warehouseCode,optional"`
	NotifyRegion  string `form:"notifyRegion,optional"`
}

type GetWarehouseStockSyncsResp struct {
	Data []WarehouseStockSync `json:"data"`
}

type CreateWarehouseStockSyncReq struct {
	Region        string `json:"region"`        // 站点代码，如 TH
	WarehouseCode string `json:"warehouseCode"` // 仓库编码
	NotifyRegion  string `json:"notifyRegion"`  // 通知地区
}

type UpdateWarehouseStockSyncReq struct {
	Id           int64  `path:"id"`
	Region       string `path:"region"`       // 站点代码，如 TH
	NotifyRegion string `json:"notifyRegion"` // 通知地区
}

type DeleteWarehouseStockSyncReq struct {
	Region string `json:"region"`
	Id     int64  `path:"id"`
}

type SyncShopOrderWithChannelReq struct {
	Region  string `path:"region"`
	ShopId  string `path:"shop_id"`
	OrderId string `path:"order_id"`
}

type SyncShopOrderWithChannelResp struct {
	Success bool   `json:"success"`
	Error   string `json:"errorMessage,optional"`
}

type PatchShopStatusReq struct {
	Region string `path:"region"`
	ShopId int64  `path:"id"`
	Status string `json:"status"`
}

type PatchShopStatusResp struct {
	Success bool   `json:"success"`
	Error   string `json:"errorMessage,optional"`
}

type GetThirdProductsReq struct {
	Region   string `path:"region"`
	ShopId   int64  `path:"shop_id"`
	Page     int    `form:"page,optional"`
	PageSize int    `form:"pageSize,optional"`
}

type GetThirdProductsResp struct {
	Data  []ThirdProduct `json:"data"`
	Total int64          `json:"total"`
}

type ThirdProduct struct {
	Id            string `json:"id"`
	ShopId        string `json:"shop_id"`
	ProductId     string `json:"product_id"`
	ProductName   string `json:"product_name"`
	ProductSku    string `json:"product_sku"`
	ProductStatus string `json:"product_status"`
}

type ReturnMerchandiseBackWarehouseReq struct {
	Region        string `path:"region"`
	WarehouseInNo string `path:"warehouse_in_no"`
}

type ReturnMerchandiseBackWarehouseResp struct {
	Success bool `json:"success"`
}

type UpdateDeliverItemSalesTransactionReq struct {
	Region              string `path:"region"`
	RelOrderNo          string `json:"relOrderNo"`
	Sku                 string `json:"sku"`
	SubjectCode         string `json:"subjectCode"`
	ChangeQuantity      int    `json:"changeQuantity"`
	CreateTime          string `json:"createTime"`
	TransactionLinkCode string `json:"transactionLinkCode"`
}

type UpdateDeliverItemSalesTransactionResp struct {
	Success bool   `json:"success"`
	Message string `json:"message"`
}

type Translation struct {
	Id          int64              `json:"id"`
	Key         string             `json:"key"`
	Namespace   string             `json:"namespace,optional"`
	DefaultText string             `json:"default_text"`
	CreatedAt   string             `json:"created_at"`
	Values      []TranslationValue `json:"values,optional"`
}

type TranslationValue struct {
	Id             int64       `json:"id"`
	TranslationId  int64       `json:"translation_id"`
	LanguageCode   string      `json:"language_code"`
	TranslatedText string      `json:"translated_text"`
	Status         string      `json:"status"`
	UpdatedAt      string      `json:"updated_at"`
	Translation    Translation `json:"translation"`
}

type GetTranslationsReq struct {
	Namespace string `form:"namespace,optional"`
	Key       string `form:"key,optional"`
	Status    string `form:"status,optional"`
	Search    string `form:"search,optional"`
	Page      int64  `form:"current,optional,default=1"`
	Limit     int64  `form:"pageSize,optional,default=20"`
}

type GetTranslationsResp struct {
	Data  []Translation `json:"data"`
	Total int           `json:"total"`
}

type CreateTranslationReq struct {
	Key         string `json:"key"`
	Namespace   string `json:"namespace"`
	DefaultText string `json:"default_text"`
}

type UpdateTranslationReq struct {
	Id          int64  `path:"id"`
	Key         string `json:"key"`
	Namespace   string `json:"namespace,optional"`
	DefaultText string `json:"default_text"`
}

type GetTranslationValuesReq struct {
	TranslationId int64  `form:"translation_id,optional"`
	LanguageCode  string `form:"language_code,optional"`
	Namespace     string `form:"namespace,optional"`
	Key           string `form:"key,optional"`
	Status        string `form:"status,optional"`
	Page          int64  `form:"current,optional,default=1"`
	Limit         int64  `form:"pageSize,optional,default=20"`
}

type GetTranslationValuesResp struct {
	Data  []TranslationValue `json:"data"`
	Total int                `json:"total"`
}

type CreateTranslationValueReq struct {
	TranslationId  int64  `json:"translation_id"`
	LanguageCode   string `json:"language_code"`
	TranslatedText string `json:"translated_text"`
	Status         string `json:"status,optional"`
}

type UpdateTranslationValueReq struct {
	Id             int64  `path:"id"`
	TranslatedText string `json:"translated_text,optional"`
	Status         string `json:"status,optional"`
}

type IdReq struct {
	Id int64 `path:"id"`
}

type ExportTranslationsReq struct {
	LanguageCode string `form:"language_code"`
	Namespace    string `form:"namespace,optional"`
	Device       string `form:"device,optional"`
}

type ExportTranslationsResp struct {
	Data string `json:"data"`
}

type AITranslationReq struct {
	Id                   int64    `path:"id"`
	LanguageCode         string   `json:"language_code"`
	PreviousTranslations []string `json:"previous_translations,optional"`
}

type AITranslationResp struct {
	Data string `json:"data"`
}

type GenerateTranslationKeyReq struct {
	DefaultText string `json:"default_text"`
	Module      string `json:"module,optional"`  // 功能模块/页面/菜单等
	Context     string `json:"context,optional"` // 额外的上下文信息
}

type GenerateTranslationKeyResp struct {
	Key string `json:"key"`
}

type CheckTranslationKeyReq struct {
	Key       string `form:"key"`
	Namespace string `form:"namespace"`
}

type CheckTranslationKeyResp struct {
	Exists      bool        `json:"exists"`
	Translation Translation `json:"translation,optional"`
}

type ErrorCode struct {
	Name    string `json:"name"`
	Code    int    `json:"code"`
	Message string `json:"message"`
}

type ImportFromErrorCodesReq struct {
	ErrorCodes []ErrorCode `json:"error_codes"`
}

type ImportFromErrorCodesResp struct {
	Success bool   `json:"success"`
	Message string `json:"errorMessage"`
}

type InfluencerVideo struct {
	Category            string  `json:"category,optional"`
	PersonInCharge      string  `json:"person_in_charge,optional"`
	ContactDate         string  `json:"contact_date,optional"`
	Platform            string  `json:"platform,optional"`
	InfluencerUsername  string  `json:"influencer_username,optional"`
	InfluencerContact   string  `json:"influencer_contact,optional"`
	InfluencerFollowers int64   `json:"influencer_followers,optional"`
	StoreName           string  `json:"store_name,optional"`
	ProductName         string  `json:"product_name,optional"`
	Sku                 string  `json:"sku,optional"`
	Status              string  `json:"status,optional"`
	Note                string  `json:"note,optional"`
	Rate                float64 `json:"rate,optional"`
	VideoLink           string  `json:"video_link,optional"`
	VideoUploadedDate   string  `json:"video_uploaded_date,optional"`
	VideoViews          int64   `json:"video_views,optional"`
	VideoLikes          int64   `json:"video_likes,optional"`
	VideoComments       int64   `json:"video_comments,optional"`
	VideoShares         int64   `json:"video_shares,optional"`
	VideoAdsCode        string  `json:"video_ads_code,optional"`
	Remark              string  `json:"remark,optional"`
	Gmv                 string  `json:"gmv,optional"`
	CreatedAt           string  `json:"created_at,optional"`
	CreatedBy           string  `json:"created_by,optional"`
	UpdatedAt           string  `json:"updated_at,optional"`
	UpdatedBy           string  `json:"updated_by,optional"`
	LastRefreshAt       string  `json:"last_refresh_at,optional"`
}

type CreateInfluencerVideoReq struct {
	InfluencerVideo
}

type CreateInfluencerVideoResp struct {
	Id int64 `json:"id"`
}

type GetInfluencerVideoReq struct {
	Id int64 `path:"id"`
}

type GetInfluencerVideoResp struct {
	Id int64 `json:"id"`
	InfluencerVideo
}

type GetInfluencerVideosReq struct {
	Id                     int64  `form:"id,optional"`
	Current                int64  `form:"current,optional"`
	PageSize               int64  `form:"page_size,optional"`
	Status                 string `form:"status,optional"`
	Platform               string `form:"platform,optional"`
	Category               string `form:"category,optional"`
	PersonInCharge         string `form:"person_in_charge,optional"`
	InfluencerUsername     string `form:"influencer_username,optional"`
	StoreName              string `form:"store_name,optional"`
	Sku                    string `form:"sku,optional"`
	VideoLink              string `form:"video_link,optional"`
	SortField              string `form:"sort_field,optional"`
	SortOrder              string `form:"sort_order,optional"`
	ContactDateStart       string `form:"contact_date_start,optional"`
	ContactDateEnd         string `form:"contact_date_end,optional"`
	VideoUploadedDateStart string `form:"video_uploaded_date_start,optional"`
	VideoUploadedDateEnd   string `form:"video_uploaded_date_end,optional"`
}

type GetInfluencerVideosResp struct {
	Data  []GetInfluencerVideoResp `json:"data"`
	Total int64                    `json:"total"`
}

type UpdateInfluencerVideoReq struct {
	Id int64 `path:"id"`
	InfluencerVideo
}

type PatchInfluencerVideoReq struct {
	Id                  int64  `path:"id"`
	InfluencerUsername  string `json:"influencer_username,optional"`
	InfluencerFollowers int64  `json:"influencer_followers,optional"`
	VideoUploadedDate   string `json:"video_uploaded_date,optional"`
	VideoViews          int64  `json:"video_views,optional"`
	VideoLikes          int64  `json:"video_likes,optional"`
	VideoComments       int64  `json:"video_comments,optional"`
	VideoShares         int64  `json:"video_shares,optional"`
}

type DeleteInfluencerVideoReq struct {
	Id int64 `path:"id"`
}

type InfluencerGmvSummary struct {
	Id                 int64   `json:"id"`
	InfluencerUsername string  `json:"influencer_username"`
	TotalGmv           float64 `json:"total_gmv"`
	CreatedAt          string  `json:"created_at"`
	UpdatedAt          string  `json:"updated_at"`
}

type GetInfluencerGmvSummaryReq struct {
	Current            int64  `form:"current,optional"`
	PageSize           int64  `form:"page_size,optional"`
	InfluencerUsername string `form:"influencer_username,optional"`
	SortField          string `form:"sort_field,optional"`
	SortOrder          string `form:"sort_order,optional"`
}

type GetInfluencerGmvSummaryResp struct {
	Data  []InfluencerGmvSummary `json:"data"`
	Total int64                  `json:"total"`
}

type CreateInfluencerGmvSummaryReq struct {
	InfluencerUsername string  `json:"influencer_username"`
	TotalGmv           float64 `json:"total_gmv"`
}

type CreateInfluencerGmvSummaryResp struct {
	Id int64 `json:"id"`
}

type UpdateInfluencerGmvSummaryReq struct {
	InfluencerUsername string  `json:"influencer_username"`
	TotalGmv           float64 `json:"total_gmv"`
}

type InfluencerVideoDailyMetrics struct {
	Id                  int64  `json:"id"`
	VideoId             int64  `json:"video_id"`
	RecordDate          string `json:"record_date"`
	InfluencerFollowers int64  `json:"influencer_followers"`
	VideoViews          int64  `json:"video_views"`
	VideoLikes          int64  `json:"video_likes"`
	VideoComments       int64  `json:"video_comments"`
	VideoShares         int64  `json:"video_shares"`
	CreatedAt           string `json:"created_at"`
	UpdatedAt           string `json:"updated_at"`
}

type GetInfluencerVideoDailyMetricsReq struct {
	VideoId   int64  `form:"video_id,optional"`
	StartDate string `form:"start_date,optional"`
	EndDate   string `form:"end_date,optional"`
	SortField string `form:"sort_field,optional"`
	SortOrder string `form:"sort_order,optional"`
	Current   int64  `form:"current,optional"`
	PageSize  int64  `form:"page_size,optional"`
}

type GetInfluencerVideoDailyMetricsResp struct {
	Data  []InfluencerVideoDailyMetrics `json:"data"`
	Total int64                         `json:"total"`
}

type CreateInfluencerVideoDailyMetricsReq struct {
	Id                  int64  `path:"id"`
	InfluencerUsername  string `json:"influencer_username,optional"`
	InfluencerFollowers int64  `json:"influencer_followers,optional"`
	VideoUploadedDate   string `json:"video_uploaded_date,optional"`
	VideoViews          int64  `json:"video_views,optional"`
	VideoLikes          int64  `json:"video_likes,optional"`
	VideoComments       int64  `json:"video_comments,optional"`
	VideoShares         int64  `json:"video_shares,optional"`
}

type CreateInfluencerVideoDailyMetricsResp struct {
	Id int64 `json:"id"`
}

type InfluencerVideoDatasource struct {
	Id         int64  `json:"id"`
	Category   string `json:"category"`
	SheetToken string `json:"sheet_token"`
	SheetId    string `json:"sheet_id"`
	CreatedAt  string `json:"created_at"`
	CreatedBy  string `json:"created_by"`
	UpdatedAt  string `json:"updated_at"`
	UpdatedBy  string `json:"updated_by"`
}

type CreateInfluencerVideoDatasourceReq struct {
	Category   string `json:"category"`
	SheetToken string `json:"sheet_token"`
	SheetId    string `json:"sheet_id"`
}

type CreateInfluencerVideoDatasourceResp struct {
	Id int64 `json:"id"`
}

type GetInfluencerVideoDatasourceReq struct {
	Id int64 `path:"id"`
}

type GetInfluencerVideoDatasourceResp struct {
	InfluencerVideoDatasource
}

type GetInfluencerVideoDatasourcesReq struct {
	Category string `form:"category,optional"`
}

type GetInfluencerVideoDatasourcesResp struct {
	Data []InfluencerVideoDatasource `json:"data"`
}

type UpdateInfluencerVideoDatasourceReq struct {
	Id         int64  `path:"id"`
	Category   string `json:"category"`
	SheetToken string `json:"sheet_token"`
	SheetId    string `json:"sheet_id"`
}

type DeleteInfluencerVideoDatasourceReq struct {
	Id int64 `path:"id"`
}

type InfluencerProductReference struct {
	Sku         string `json:"sku"`
	ProductName string `json:"product_name"`
	UpdatedAt   string `json:"updated_at"`
	CreatedAt   string `json:"created_at"`
}

type CreateInfluencerProductReferenceReq struct {
	Sku         string `json:"sku"`
	ProductName string `json:"product_name"`
}

type CreateInfluencerProductReferenceResp struct {
	Sku string `json:"sku"`
}

type GetInfluencerProductReferenceReq struct {
	Sku string `path:"sku"`
}

type GetInfluencerProductReferenceResp struct {
	InfluencerProductReference
}

type GetInfluencerProductReferencesReq struct {
	Sku         string `form:"sku,optional"`
	ProductName string `form:"product_name,optional"`
	Page        int64  `form:"page,optional,default=1"`
	PageSize    int64  `form:"page_size,optional,default=20"`
}

type GetInfluencerProductReferencesResp struct {
	Data  []InfluencerProductReference `json:"data"`
	Total int64                        `json:"total"`
}

type UpdateInfluencerProductReferenceReq struct {
	Sku         string `path:"sku"`
	ProductName string `json:"product_name"`
}

type DeleteInfluencerProductReferenceReq struct {
	Sku string `path:"sku"`
}

type BatchCreateInfluencerProductReferenceReq struct {
	References []CreateInfluencerProductReferenceReq `json:"references"`
}

type BatchCreateInfluencerProductReferenceResp struct {
	Success int64 `json:"success"`
	Failed  int64 `json:"failed"`
}

type LivePerformance struct {
	Id                 int64   `json:"id,optional"`
	Hash               string  `json:"hash,optional"`
	Shop               string  `json:"shop,optional"`
	LiveRoom           string  `json:"live_room,optional"`
	Shift              string  `json:"shift,optional"`
	Session            int     `json:"session,optional"`
	Host               string  `json:"host,optional"`
	Assistant          string  `json:"assistant,optional"`
	LivestreamTitle    string  `json:"livestream_title,optional"`
	StartTime          string  `json:"start_time,optional"`
	StartDate          string  `json:"start_date,optional"`
	StartMonth         string  `json:"start_month,optional"`
	StartWeek          string  `json:"start_week,optional"`
	Duration           float64 `json:"duration,optional"`
	DurationHours      float64 `json:"duration_hours,optional"`
	DirectGMV          float64 `json:"direct_gmv,optional"`
	DirectGMVRMB       float64 `json:"direct_gmv_rmb,optional"`
	GrossRevenue       float64 `json:"gross_revenue,optional"`
	ItemsSold          float64 `json:"items_sold,optional"`
	Buyers             float64 `json:"buyers,optional"`
	OrdersPaidFor      float64 `json:"orders_paid_for,optional"`
	ShowGPM            float64 `json:"show_gpm,optional"`
	WatchGPM           float64 `json:"watch_gpm,optional"`
	Views              float64 `json:"views,optional"`
	Viewers            float64 `json:"viewers,optional"`
	PCU                float64 `json:"pcu,optional"`
	NewFollowers       float64 `json:"new_followers,optional"`
	AVD                float64 `json:"avd,optional"`
	Likes              float64 `json:"likes,optional"`
	Comments           float64 `json:"comments,optional"`
	Shares             float64 `json:"shares,optional"`
	ProductImpressions float64 `json:"product_impressions,optional"`
	ProductClicks      float64 `json:"product_clicks,optional"`
	InteractionRate    float64 `json:"interaction_rate,optional"`
	CreatedAt          string  `json:"created_at,optional"`
	UpdatedAt          string  `json:"updated_at,optional"`
}

type CreateLivePerformanceReq struct {
	LivePerformance
}

type CreateLivePerformanceResp struct {
	Id int64 `json:"id"`
}

type GetLivePerformanceReq struct {
	Id int64 `path:"id"`
}

type GetLivePerformanceResp struct {
	Id int64 `json:"id"`
	LivePerformance
}

type GetLivePerformancesReq struct {
	Id         int64  `form:"id,optional"`
	Hash       string `form:"hash,optional"`
	Assistant  string `form:"assistant,optional"`
	Current    int64  `form:"current,optional"`
	PageSize   int64  `form:"page_size,optional"`
	Shop       string `form:"shop,optional"`
	LiveRoom   string `form:"live_room,optional"`
	Host       string `form:"host,optional"`
	Shift      string `form:"shift,optional"`
	SortField  string `form:"sort_field,optional"`
	SortOrder  string `form:"sort_order,optional"`
	StartDate  string `form:"start_date,optional"`
	EndDate    string `form:"end_date,optional"`
	StartMonth string `form:"start_month,optional"`
}

type GetLivePerformancesResp struct {
	Data  []GetLivePerformanceResp `json:"data"`
	Total int64                    `json:"total"`
}

type UpdateLivePerformanceReq struct {
	Id int64 `path:"id"`
	LivePerformance
}

type DeleteLivePerformanceReq struct {
	Id int64 `path:"id"`
}

type LivePerformanceDatasource struct {
	Id         int64  `json:"id"`
	Category   string `json:"category"`
	SheetToken string `json:"sheet_token"`
	SheetId    string `json:"sheet_id"`
	CreatedAt  string `json:"created_at"`
	CreatedBy  string `json:"created_by"`
	UpdatedAt  string `json:"updated_at"`
	UpdatedBy  string `json:"updated_by"`
}

type CreateLivePerformanceDatasourceReq struct {
	Category   string `json:"category"`
	SheetToken string `json:"sheet_token"`
	SheetId    string `json:"sheet_id"`
}

type CreateLivePerformanceDatasourceResp struct {
	Id int64 `json:"id"`
}

type GetLivePerformanceDatasourceReq struct {
	Id int64 `path:"id"`
}

type GetLivePerformanceDatasourceResp struct {
	LivePerformanceDatasource
}

type GetLivePerformanceDatasourcesReq struct {
	Category string `form:"category,optional"`
}

type GetLivePerformanceDatasourcesResp struct {
	Data []LivePerformanceDatasource `json:"data"`
}

type UpdateLivePerformanceDatasourceReq struct {
	Id         int64  `path:"id"`
	Category   string `json:"category"`
	SheetToken string `json:"sheet_token"`
	SheetId    string `json:"sheet_id"`
}

type DeleteLivePerformanceDatasourceReq struct {
	Id int64 `path:"id"`
}

type LivePerformanceGoal struct {
	Month string  `json:"month"`
	Goal  float64 `json:"goal"`
}

type CreateLivePerformanceGoalReq struct {
	Month string  `json:"month"`
	Goal  float64 `json:"goal"`
}

type CreateLivePerformanceGoalResp struct {
	Month string `json:"month"`
}

type GetLivePerformanceGoalReq struct {
	Month string `path:"month"`
}

type GetLivePerformanceGoalResp struct {
	LivePerformanceGoal
}

type GetLivePerformanceGoalsReq struct {
	Month string `form:"month,optional"`
}

type GetLivePerformanceGoalsResp struct {
	Data []LivePerformanceGoal `json:"data"`
}

type UpdateLivePerformanceGoalReq struct {
	Month string  `path:"month"`
	Goal  float64 `json:"goal"`
}

type DeleteLivePerformanceGoalReq struct {
	Month string `path:"month"`
}

type SyncLivePerformanceReq struct {
	Hashes []string `json:"hashes"`
}

type SyncLivePerformanceResp struct {
	Deleted []string `json:"deleted"`
	Updated []string `json:"updated"`
	Created []string `json:"created"`
}

type PatchInfluencerVideoHashReq struct {
	Id int64 `path:"id"`
}

type PatchInfluencerVideoHashResp struct {
	Id int64 `json:"id"`
}

type IDReq struct {
	Id int64 `path:"id"`
}

type NameReq struct {
	Name string `path:"name"`
}

type BaseResponse struct {
	Code int    `json:"code"`
	Msg  string `json:"msg"`
}

type ApplicationBranch struct {
	ApplicationId int64  `json:"application_id"`
	BranchName    string `json:"branch_name"`
}

type GitLabRepository struct {
	Id          int64  `json:"id"`
	Name        string `json:"name"`
	Url         string `json:"url"`
	Description string `json:"description"`
	IsActive    bool   `json:"is_active"`
}

type GetGitLabRepositoryResp struct {
	Data GitLabRepository `json:"data"`
}

type GetRepositoriesResp struct {
	Data []GitLabRepository `json:"data"`
}

type JenkinsJob struct {
	Name        string `json:"name"`
	Url         string `json:"url"`
	Class       string `json:"class,optional"`
	JobFullPath string `json:"jobFullPath,optional"`
}

type GetJenkinsJobListReq struct {
	Page     int `form:"page,optional"`
	PageSize int `form:"page_size,optional"`
}

type GetJenkinsJobListResp struct {
	Data []JenkinsJob `json:"data"`
}

type GetJenkinsJobsInFolderReq struct {
	FolderPath string `path:"folderPath"`
	Page       int    `form:"page,optional"`
	PageSize   int    `form:"page_size,optional"`
}

type GetJenkinsJobResp struct {
	Data JenkinsJob `json:"data"`
}

type BuildJenkinsJobReq struct {
	JobFullPath string `path:"jobFullPath"`
	Branch      string `json:"branch"`
}

type Application struct {
	Id                 int64          `json:"id"`
	Name               string         `json:"name"`
	Description        string         `json:"description"`
	GitlabProjectId    int64          `json:"gitlab_project_id"`
	GitlabRepoName     string         `json:"gitlab_repo_name"`
	GitlabRepoUrl      string         `json:"gitlab_repo_url"`
	JenkinsJobFullPath string         `json:"jenkins_job_full_path"`
	ConfigFilePath     string         `json:"config_file_path"`
	Configmap          string         `json:"configmap"`
	ConfigmapKey       string         `json:"configmap_key"`
	GitlabRepoBranches []GitLabBranch `json:"gitlab_repo_branches,optional"`
	IsActive           bool           `json:"is_active"`
	CreatedAt          string         `json:"created_at"`
	UpdatedAt          string         `json:"updated_at"`
}

type GetApplicationResp struct {
	Data Application `json:"data"`
}

type CreateApplicationReq struct {
	Name               string `json:"name"`
	Description        string `json:"description"`
	GitlabProjectId    int64  `json:"gitlab_project_id"`
	GitlabRepoName     string `json:"gitlab_repo_name"`
	GitlabRepoUrl      string `json:"gitlab_repo_url"`
	JenkinsJobFullPath string `json:"jenkins_job_full_path"`
	ConfigFilePath     string `json:"config_file_path"`
	Configmap          string `json:"configmap"`
	ConfigmapKey       string `json:"configmap_key"`
	IsActive           bool   `json:"is_active"`
}

type GetApplicationsReq struct {
	Name string `form:"name,optional"`
}

type GetApplicationsResp struct {
	Data []Application `json:"data"`
}

type UpdateApplicationReq struct {
	Id int64 `path:"id"`
	CreateApplicationReq
}

type GitLabBranch struct {
	Name   string `json:"name"`
	Commit string `json:"commit"`
}

type GetGitLabBranchesReq struct {
	RepositoryId int64 `form:"repository_id"`
}

type GetGitLabBranchesResp struct {
	Data []GitLabBranch `json:"data"`
}

type GetConfigMapsReq struct {
	Namespace string `form:"namespace,optional"`
}

type GetConfigMapsResp struct {
	Data []ConfigMapInfo `json:"data"`
}

type ConfigMapContent struct {
	Key     string `json:"key"`
	Content string `json:"content"`
}

type ConfigMapInfo struct {
	Name     string             `json:"name"`
	Contents []ConfigMapContent `json:"contents"`
}

type GetConfigMapContentReq struct {
	Namespace string `form:"namespace,optional"`
	Name      string `form:"name"`
	Key       string `form:"key"`
}

type GetConfigMapContentResp struct {
	Content string `json:"content"`
}

type GetApplicationConfigFileContentReq struct {
	Id     int64  `path:"id"`
	Branch string `form:"branch"`
}

type GetApplicationConfigFileContentResp struct {
	Content string `json:"content"`
}

type GetApplicationConfigMapContentReq struct {
	Id int64 `path:"id"`
}

type GetApplicationConfigMapContentResp struct {
	Content string `json:"content"`
}

type AIConfigCompareReq struct {
	GitContent       string `json:"git_content"`
	ConfigMapContent string `json:"configmap_content"`
	ApplicationName  string `json:"application_name"`
}

type AIConfigCompareResp struct {
	Differences []string `json:"differences"`
	Summary     string   `json:"summary"`
	Suggestions []string `json:"suggestions"`
}

type CreateReleaseApprovalReq struct {
	ReleaseId      int64  `json:"release_id"`
	Title          string `json:"title"`
	Environment    string `json:"environment"`
	Description    string `json:"description"`
	ApprovalFlowId int64  `json:"approval_flow_id"`
}

type CreateReleaseApprovalResp struct {
	ApprovalInstanceId int64  `json:"approval_instance_id"`
	Status             string `json:"status"`
}

type ReleasePlan struct {
	Id                 string                   `json:"id"`
	Name               string                   `json:"name"`
	Description        string                   `json:"description"`
	ApprovalFlowId     int64                    `json:"approval_flow_id"`
	ApprovalInstanceId int64                    `json:"approval_instance_id"`
	MergeRequestUrls   []string                 `json:"merge_request_urls"`
	PrdUrls            []string                 `json:"prd_urls"`
	TechDesignUrls     []string                 `json:"tech_design_urls"`
	ReleaseManualUrls  []string                 `json:"release_manual_urls"`
	Status             string                   `json:"status"`
	CreatedAt          string                   `json:"created_at"`
	UpdatedAt          string                   `json:"updated_at"`
	CreatedBy          string                   `json:"created_by"`
	ApplicationTasks   []ReleaseApplicationTask `json:"application_tasks,optional"`
	DbMigrationTasks   []ReleaseDbMigrationTask `json:"db_migration_tasks,optional"`
}

type CreateReleasePlanReq struct {
	Name              string                     `json:"name"`
	Description       string                     `json:"description"`
	ApprovalFlowId    int64                      `json:"approval_flow_id"`
	MergeRequestUrls  []string                   `json:"merge_request_urls"`
	PrdUrls           []string                   `json:"prd_urls"`
	TechDesignUrls    []string                   `json:"tech_design_urls"`
	ReleaseManualUrls []string                   `json:"release_manual_urls"`
	ApplicationTasks  []CreateApplicationTaskReq `json:"application_tasks"`
	DbMigrationTasks  []CreateDbMigrationTaskReq `json:"db_migration_tasks"`
}

type CreateApplicationTaskReq struct {
	ApplicationId int64  `json:"application_id"`
	GitBranch     string `json:"git_branch"`
	ConfigData    string `json:"config_data,optional"`
}

type CreateDbMigrationTaskReq struct {
	SqlScript string `json:"sql_script"`
	Site      string `json:"site"`
	Region    string `json:"region"`
}

type ReleaseApplicationTask struct {
	Id                 string `json:"id"`
	PlanId             string `json:"plan_id"`
	ApplicationId      int64  `json:"application_id"`
	GitBranch          string `json:"git_branch"`
	ConfigData         string `json:"config_data,optional"`
	JenkinsQueueItemId string `json:"jenkins_queue_item_id"`
	JenkinsBuildNumber int64  `json:"jenkins_build_number,optional"`
	JenkinsBuildStatus string `json:"jenkins_build_status,optional"`
	Status             string `json:"status"`
	Executed           bool   `json:"executed"`
	ExecutedAt         string `json:"executed_at"`
	CreatedAt          string `json:"created_at"`
	UpdatedAt          string `json:"updated_at"`
}

type ReleaseDbMigrationTask struct {
	Id         string `json:"id"`
	PlanId     string `json:"plan_id"`
	SqlScript  string `json:"sql_script"`
	Site       string `json:"site"`
	Region     string `json:"region"`
	Status     string `json:"status"`
	Executed   bool   `json:"executed"`
	ExecutedAt string `json:"executed_at"`
	CreatedAt  string `json:"created_at"`
	UpdatedAt  string `json:"updated_at"`
}

type GetReleasePlansReq struct {
	Name   string `form:"name,optional"`
	Status string `form:"status,optional"`
}

type GetReleasePlansResp struct {
	Data []ReleasePlan `json:"data"`
}

type PlanIdReq struct {
	PlanId string `path:"plan_id"`
}

type TaskIdReq struct {
	TaskId string `path:"task_id"`
}

type UpdatePlanStatusReq struct {
	PlanId string `path:"plan_id"`
	Status string `json:"status"`
}

type ExecuteTaskResp struct {
	TaskId             string `json:"task_id"`
	JenkinsQueueItemId string `json:"jenkins_queue_item_id,optional"`
	Success            bool   `json:"success"`
	Message            string `json:"errorMessage"`
}

type ExecuteAllTasksResp struct {
	PlanId          string   `json:"plan_id"`
	ExecutedTaskIds []string `json:"executed_task_ids"`
	FailedTaskIds   []string `json:"failed_task_ids"`
	Message         string   `json:"message"`
}

type GetApplicationTaskResp struct {
	Data ReleaseApplicationTask `json:"data"`
}

type AclSystemInfo struct {
	Name        string `json:"name"`
	Description string `json:"description"`
}

type AclRoleInfo struct {
	Description string              `json:"description"`
	Systems     map[string][]string `json:"systems"`
}

type AclConfigData struct {
	Systems   map[string]AclSystemInfo `json:"systems"`
	Roles     map[string]AclRoleInfo   `json:"roles"`
	UserRoles map[string][]string      `json:"userRoles"`
	Metadata  map[string]string        `json:"metadata"`
}

type GetAclConfigResp struct {
	Data AclConfigData `json:"data"`
}

type UpdateAclConfigReq struct {
	Systems   map[string]AclSystemInfo `json:"systems"`
	Roles     map[string]AclRoleInfo   `json:"roles"`
	UserRoles map[string][]string      `json:"userRoles"`
}

type UpdateAclConfigResp struct {
	Success bool   `json:"success"`
	Message string `json:"message"`
	Version string `json:"version"`
}

type CheckPermissionReq struct {
	Email  string `json:"email"`
	System string `json:"system"`
	Path   string `json:"path"`
}

type CheckPermissionResp struct {
	Allowed bool     `json:"allowed"`
	Roles   []string `json:"roles"`
	System  string   `json:"system"`
	Path    string   `json:"path"`
}

type GetUserPermissionsReq struct {
	Email string `form:"email,optional"`
}

type UserPermissionInfo struct {
	Email       string              `json:"email"`
	Roles       []string            `json:"roles"`
	Permissions map[string][]string `json:"permissions"`
}

type GetUserPermissionsResp struct {
	Data UserPermissionInfo `json:"data"`
}

type PathInfo struct {
	Path        string `json:"path"`
	System      string `json:"system"`
	Method      string `json:"method"`
	Description string `json:"description"`
}

type GetAvailablePathsResp struct {
	Data []PathInfo `json:"data"`
}

type GetRolesResp struct {
	Data []RoleInfo `json:"data"`
}

type RoleInfo struct {
	Name        string              `json:"name"`
	Description string              `json:"description"`
	Systems     map[string][]string `json:"systems"`
}

type CreateRoleReq struct {
	Name        string              `json:"name"`
	Description string              `json:"description"`
	Systems     map[string][]string `json:"systems"`
}

type CreateRoleResp struct {
	Success bool     `json:"success"`
	Data    RoleInfo `json:"data"`
	Message string   `json:"message"`
}

type UpdateRoleReq struct {
	Name        string              `path:"name"`
	Description string              `json:"description,optional"`
	Systems     map[string][]string `json:"systems,optional"`
}

type UpdateRoleResp struct {
	Success bool     `json:"success"`
	Data    RoleInfo `json:"data"`
	Message string   `json:"message"`
}

type DeleteRoleResp struct {
	Success bool   `json:"success"`
	Message string `json:"message"`
}

type AssignUserRolesReq struct {
	Assignments []UserRoleAssignment `json:"assignments"`
}

type UserRoleAssignment struct {
	Email string `json:"email"`
	Role  string `json:"role"`
}

type AssignUserRolesResp struct {
	Success       bool   `json:"success"`
	AssignedCount int    `json:"assignedCount"`
	Message       string `json:"message"`
}

type UserRoles struct {
	Email string   `json:"email"`
	Roles []string `json:"roles"`
}

type GetUsersRolesReq struct {
	Emails []string `json:"emails,optional"`
}

type GetUsersRolesResp struct {
	Data []UserRoles `json:"data"`
}

type RoleNameReq struct {
	Name string `path:"name"`
}

type OndutyMember struct {
	Email string `json:"email"`
	Name  string `json:"name"`
}

type OndutyPair struct {
	Primary   OndutyMember `json:"primary"`
	Secondary OndutyMember `json:"secondary"`
}

type DailyOndutySchedule struct {
	Date      string       `json:"date"`
	Primary   OndutyMember `json:"primary"`
	Secondary OndutyMember `json:"secondary"`
}

type AddOndutyPairReq struct {
	Primary   OndutyMember `json:"primary"`
	Secondary OndutyMember `json:"secondary"`
}

type GetCurrentOndutyResp struct {
	DailyOndutySchedule
}

type GetMonthlyOndutyReq struct {
	Year  int64 `form:"year"`
	Month int64 `form:"month"`
}

type GetMonthlyOndutyResp struct {
	Schedules []DailyOndutySchedule `json:"schedules"`
}

type ListOndutyPairsResp struct {
	Pairs []OndutyPair `json:"pairs"`
}

type UpdateOndutyPairOrderReq struct {
	Pairs []OndutyPair `json:"pairs"` // The complete list of pairs in the desired rotation order
}

type DeleteOndutyPairReq struct {
	Primary   OndutyMember `json:"primary"`
	Secondary OndutyMember `json:"secondary"`
}

type GetOndutyScheduleByEmailReq struct {
	Email     string `form:"email"`
	StartDate string `form:"start_date,optional"` // Format: YYYY-MM-DD
	EndDate   string `form:"end_date,optional"`   // Format: YYYY-MM-DD
}

type GetOndutyScheduleByEmailResp struct {
	Schedules []DailyOndutySchedule `json:"schedules"`
}

type StringIDReq struct {
	Id string `path:"id"`
}

type ApprovalFlow struct {
	Id          int64              `json:"id"`
	Name        string             `json:"name"`
	Description string             `json:"description"`
	Enabled     bool               `json:"enabled"`
	CreatedAt   string             `json:"created_at"`
	UpdatedAt   string             `json:"updated_at"`
	Steps       []ApprovalFlowNode `json:"steps,optional"`
}

type ApprovalFlowNode struct {
	Id          int64    `json:"id"`
	FlowId      int64    `json:"flow_id"`
	Level       int      `json:"level"`
	NodeType    string   `json:"node_type"` // single/and/or
	Approvers   []string `json:"approvers"` // 审批人邮箱列表
	Optional    bool     `json:"optional"`
	Description string   `json:"description"`
	CreatedAt   string   `json:"created_at"`
}

type CreateApprovalFlowReq struct {
	Name        string                   `json:"name"`
	Description string                   `json:"description"`
	Enabled     bool                     `json:"enabled"`
	Steps       []CreateApprovalFlowStep `json:"steps"`
}

type CreateApprovalFlowStep struct {
	Level       int      `json:"level"`
	NodeType    string   `json:"node_type"` // single/and/or
	Approvers   []string `json:"approvers"` // 审批人邮箱列表
	Optional    bool     `json:"optional"`
	Description string   `json:"description"`
}

type GetApprovalFlowsReq struct {
	Enabled string `form:"enabled,optional"`
	Page    int    `form:"page,optional"`
	Limit   int    `form:"limit,optional"`
}

type GetApprovalFlowsResp struct {
	List  []ApprovalFlow `json:"list"`
	Total int64          `json:"total"`
}

type GetApprovalFlowReq struct {
	Id int64 `path:"id"`
}

type UpdateApprovalFlowReq struct {
	Id          int64                    `path:"id"`
	Name        string                   `json:"name"`
	Description string                   `json:"description"`
	Enabled     bool                     `json:"enabled"`
	Steps       []CreateApprovalFlowStep `json:"steps"`
}

type DeleteApprovalFlowReq struct {
	Id int64 `path:"id"`
}

type ApprovalProgress struct {
	Status string                 `json:"status"`
	Steps  []ApprovalProgressStep `json:"steps"`
}

type ApprovalProgressStep struct {
	Level     int                      `json:"level"`
	NodeType  string                   `json:"node_type"`
	Approvers []string                 `json:"approvers"`
	Status    string                   `json:"status"`
	Details   []ApprovalProgressDetail `json:"details"`
}

type ApprovalProgressDetail struct {
	ApproverEmail string `json:"approver_email"`
	Status        string `json:"status"`
	Comment       string `json:"comment,optional"`
	ApprovedAt    string `json:"approved_at,optional"`
}

type ApprovalInstance struct {
	Id           int64  `json:"id"`
	FlowId       int64  `json:"flow_id"`
	BizType      string `json:"biz_type"`
	BizObjectId  string `json:"biz_object_id"`
	Status       string `json:"status"`
	CurrentLevel int    `json:"current_level"`
	StartedAt    string `json:"started_at"`
	CompletedAt  string `json:"completed_at,optional"`
	CreatedBy    string `json:"created_by"`
}

type GetApprovalInstanceReq struct {
	Id int64 `path:"id"`
}

type GetApprovalInstancesReq struct {
	Status      string `form:"status,optional"`
	BizType     string `form:"biz_type,optional"`
	BizObjectId string `form:"biz_object_id,optional"`
	Page        int    `form:"page,optional"`
	Limit       int    `form:"limit,optional"`
}

type GetApprovalInstancesResp struct {
	List  []ApprovalInstance `json:"list"`
	Total int64              `json:"total"`
}

type GetApprovalInstanceTasksResp struct {
	List []ApprovalTask `json:"list"`
}

type ApprovalTask struct {
	Id                int64                  `json:"id"`
	InstanceId        int64                  `json:"instance_id"`
	Level             int                    `json:"level"`
	NodeType          string                 `json:"node_type"`
	Approvers         []string               `json:"approvers"`
	Status            string                 `json:"status"`
	ApprovedBy        []ApprovalTaskApprover `json:"approved_by"`
	CreatedAt         string                 `json:"created_at"`
	UpdatedAt         string                 `json:"updated_at"`
	BizType           string                 `json:"biz_type"`
	BizObjectId       string                 `json:"biz_object_id"`
	InstanceStatus    string                 `json:"instance_status"`
	InstanceCreatedBy string                 `json:"instance_created_by"`
	Optional          bool                   `json:"optional"`
}

type ApprovalTaskApprover struct {
	ApproverEmail string `json:"approver_email"`
	Decision      string `json:"decision"`
	Comment       string `json:"comment,optional"`
	ApprovedAt    string `json:"approved_at"`
}

type GetMyApprovalTasksReq struct {
	Status string `form:"status,optional"`
	Page   int    `form:"page,optional"`
	Limit  int    `form:"limit,optional"`
}

type GetMyApprovalTasksResp struct {
	List  []ApprovalTask `json:"list"`
	Total int64          `json:"total"`
}

type GetAllApprovalTasksReq struct {
	Status string `form:"status,optional"`
	Page   int    `form:"page,optional"`
	Limit  int    `form:"limit,optional"`
}

type GetAllApprovalTasksResp struct {
	List  []ApprovalTask `json:"list"`
	Total int64          `json:"total"`
}

type GetApprovalTaskReq struct {
	Id int64 `path:"id"`
}

type ApproveTaskReq struct {
	TaskId  int64  `path:"task_id"`
	Comment string `json:"comment,optional"`
}

type ApproveTaskResp struct {
	Success     bool   `json:"success"`
	Message     string `json:"errorMessage"`
	NextStep    int    `json:"next_step,optional"`
	IsCompleted bool   `json:"is_completed"`
}

type RejectTaskReq struct {
	TaskId  int64  `path:"task_id"`
	Comment string `json:"comment,optional"`
}

type RejectTaskResp struct {
	Success bool   `json:"success"`
	Message string `json:"errorMessage"`
}

type SkipTaskReq struct {
	TaskId  int64  `path:"task_id"`
	Comment string `json:"comment,optional"`
}

type SkipTaskResp struct {
	Success     bool   `json:"success"`
	Message     string `json:"errorMessage"`
	NextStep    int    `json:"next_step,optional"`
	IsCompleted bool   `json:"is_completed"`
}

type ListXxlExecutorsResp struct {
	Data []XxlExecutor `json:"data"`
}

type XxlExecutor struct {
	Id           int      `json:"id"`
	AppName      string   `json:"appName"`
	Title        string   `json:"title"`
	AddressType  int      `json:"addressType"`
	AddressList  string   `json:"addressList"`
	UpdateTime   string   `json:"updateTime"`
	RegistryList []string `json:"registryList"`
}

type CopyJobsFromIndonesiaReq struct {
	Id      int    `path:"id"`
	AppName string `json:"appName,optional"`
}

type CopyJobsFromIndonesiaResp struct {
	Success bool     `json:"success"`
	Message string   `json:"errorMessage"`
	Data    []XxlJob `json:"data"`
}

type XxlJob struct {
	Name            string `json:"name"`
	Description     string `json:"description"`
	Cron            string `json:"cron"`
	JobGroup        int    `json:"jobGroup"`
	JobDesc         string `json:"jobDesc"`
	AddTime         string `json:"addTime"`
	UpdateTime      string `json:"updateTime"`
	Author          string `json:"author"`
	AlarmEmail      string `json:"alarmEmail"`
	AlarmStatus     int    `json:"alarmStatus"`
	ExecutorHandler string `json:"executorHandler"`
}

type DMSQueryReq struct {
	System string `json:"system"`
	Region string `json:"region"`
	SQL    string `json:"sql,optional"`
}

type DMSQueryResult struct {
	Columns []string   `json:"columns"`
	Rows    [][]string `json:"rows"`
	Error   string     `json:"error,optional"`
}

type ExecuteDMSMutationReq struct {
	System string `json:"system"`
	Region string `json:"region"`
	SQL    string `json:"sql"`
}

type ExecuteDMSMutationResp struct {
	Success       bool   `json:"success"`
	Message       string `json:"message,optional"`
	AffectedRows  int64  `json:"affected_rows,optional"`
	ExecutionTime string `json:"execution_time,optional"`
	SqlStatement  string `json:"sql_statement,optional"`
}

type GetPresignedUrlReq struct {
	FileName    string `form:"file_name"`
	ContentType string `form:"content_type"`
}

type GetPresignedUrlResp struct {
	Region          string `json:"region"`
	Bucket          string `json:"bucket"`
	Expiration      string `json:"expiration"`
	KeyPrefix       string `json:"keyPrefix"`
	AccessKeyId     string `json:"accessKeyId"`
	AccessKeySecret string `json:"accessKeySecret"`
	SecurityToken   string `json:"securityToken"`
}

type OhsomeApkVersion struct {
	Version     string `json:"version"`
	FileName    string `json:"fileName"`
	DownloadUrl string `json:"downloadUrl"`
	UploadTime  string `json:"uploadTime"`
	FileSize    int64  `json:"fileSize"`
}

type GetOhsomeApkVersionsResp struct {
	Data []OhsomeApkVersion `json:"data"`
}

type GetO2OGrabConfigListResp struct {
	Data []O2OGrabConfig `json:"data"`
}

type O2OGrabConfig struct {
	Region          string `json:"region"`
	AccessKeyId     string `json:"accessKeyId"`
	AccessKeySecret string `json:"accessKeySecret"`
	TokenPoint      string `json:"tokenPoint"`
	ApiPointPrefix  string `json:"apiPointPrefix"`
}

type AddO2OGrabConfigReq struct {
	Region          string `json:"region"`
	AccessKeyId     string `json:"accessKeyId"`
	AccessKeySecret string `json:"accessKeySecret"`
}

type AddO2OGrabConfigResp struct {
	Success bool   `json:"success"`
	Message string `json:"message"`
}

type UpdateO2OGrabConfigReq struct {
	Region          string `json:"region"`
	AccessKeyId     string `json:"accessKeyId"`
	AccessKeySecret string `json:"accessKeySecret"`
}

type UpdateO2OGrabConfigResp struct {
	Success bool   `json:"success"`
	Message string `json:"message"`
}

type DeleteO2OGrabConfigReq struct {
	Region string `json:"region"`
}

type DeleteO2OGrabConfigResp struct {
	Success bool   `json:"success"`
	Message string `json:"message"`
}

type RemoveLogoFromImageReq struct {
	ImageUrl string `json:"imageUrl"`
}

type RemoveLogoFromImageResp struct {
	Success     bool   `json:"success"`
	Message     string `json:"errorMessage,optional"`
	ImageData   string `json:"imageData,optional"`   // base64编码的图片数据
	ContentType string `json:"contentType,optional"` // 图片类型
}

type RocketMQTopic struct {
	TopicName   string `json:"topicName"`
	MessageType string `json:"messageType"`
	Remark      string `json:"remark,optional"`
	CreateTime  string `json:"createTime,optional"`
	UpdateTime  string `json:"updateTime,optional"`
	Status      string `json:"status,optional"`
}

type RocketMQConsumerGroup struct {
	ConsumerGroupId string `json:"consumerGroupId"`
	Remark          string `json:"remark,optional"`
	CreateTime      string `json:"createTime,optional"`
	UpdateTime      string `json:"updateTime,optional"`
	Status          string `json:"status,optional"`
}

type ListRocketMQTopicsResp struct {
	Success bool            `json:"success"`
	Message string          `json:"errorMessage,optional"`
	Data    []RocketMQTopic `json:"data"`
}

type ListRocketMQConsumerGroupsResp struct {
	Success bool                    `json:"success"`
	Message string                  `json:"errorMessage,optional"`
	Data    []RocketMQConsumerGroup `json:"data"`
}

type CreateRocketMQTopicReq struct {
	TopicName   string `json:"topicName"`
	MessageType string `json:"messageType,optional,default=NORMAL"`
	Remark      string `json:"remark,optional"`
}

type CreateRocketMQTopicResp struct {
	Success bool   `json:"success"`
	Message string `json:"errorMessage,optional"`
}

type CreateRocketMQConsumerGroupReq struct {
	ConsumerGroupId string `json:"consumerGroupId"`
	Remark          string `json:"remark,optional"`
}

type CreateRocketMQConsumerGroupResp struct {
	Success bool   `json:"success"`
	Message string `json:"errorMessage,optional"`
}

type DeleteRocketMQTopicReq struct {
	TopicName string `path:"topicName"`
}

type DeleteRocketMQTopicResp struct {
	Success bool   `json:"success"`
	Message string `json:"errorMessage,optional"`
}

type DeleteRocketMQConsumerGroupReq struct {
	ConsumerGroupId string `path:"consumerGroupId"`
}

type DeleteRocketMQConsumerGroupResp struct {
	Success bool   `json:"success"`
	Message string `json:"errorMessage,optional"`
}

type GetFeatureControlConfigResp struct {
	Success bool                 `json:"success"`
	Message string               `json:"errorMessage,optional"`
	Data    FeatureControlConfig `json:"data"`
}

type UpdateFeatureControlConfigReq struct {
	FeatureToggle FeatureToggle `json:"feature_toggle"`
}

type UpdateFeatureControlConfigResp struct {
	Success bool   `json:"success"`
	Message string `json:"errorMessage,optional"`
}

type FeatureControlConfig struct {
	FeatureToggle FeatureToggle `json:"feature_toggle"`
}

type FeatureToggle struct {
	Enabled    bool                       `json:"enabled"`
	Namespaces map[string]NamespaceConfig `json:"namespaces"`
}

type NamespaceConfig struct {
	Description string                   `json:"description"`
	Enabled     bool                     `json:"enabled"`
	Features    map[string]FeatureConfig `json:"features"`
}

type FeatureConfig struct {
	Enabled bool                   `json:"enabled"`
	Config  map[string]interface{} `json:"-"`
}

type FunifunRewardItem struct {
	Id              int64  `json:"id,optional"`               // 商品ID
	Name            string `json:"name"`                      // 商品名称
	DisplayOrder    int64  `json:"display_order"`             // 展示顺序
	RequiredTickets int64  `json:"required_tickets,optional"` // 所需彩票数
	RequiredStars   int64  `json:"required_stars,optional"`   // 所需星星数
	Status          string `json:"status"`                    // 状态：active-上架, inactive-下架
	Description     string `json:"description,optional"`      // 商品描述
	MainImageUrl    string `json:"main_image_url,optional"`   // 主图
	Sku             string `json:"sku"`                       // SKU编码
	CreatedAt       string `json:"created_at,optional"`       // 创建时间
	UpdatedAt       string `json:"updated_at,optional"`       // 更新时间
	CreatedBy       int64  `json:"created_by,optional"`       // 创建人
	CreatedByName   string `json:"created_by_name,optional"`  // 创建人姓名
	UpdatedBy       int64  `json:"updated_by,optional"`       // 更新人
	UpdatedByName   string `json:"updated_by_name,optional"`  // 更新人姓名
	DeletedBy       int64  `json:"deleted_by,optional"`       // 删除人
	DeletedByName   string `json:"deleted_by_name,optional"`  // 删除人姓名
	DeletedAt       string `json:"deleted_at,optional"`       // 删除时间
	FlagDeleted     bool   `json:"flag_deleted,optional"`     // 是否删除：0-未删除，1-已删除
}

type ListFunifunRewardItemsReq struct {
	Region   string `form:"region,optional"`
	Page     int64  `form:"page,optional"`
	PageSize int64  `form:"page_size,optional"`
}

type ListFunifunRewardItemsResp struct {
	Total int64               `json:"total"` // 总数
	List  []FunifunRewardItem `json:"list"`  // 列表
}

type SyncFunifunRewardItemsReq struct {
	Id           int64  `path:"id"`
	Region       string `path:"region"`
	TargetRegion string `path:"targetRegion"`
}

type SyncFunifunRewardItemsResp struct {
	Success bool   `json:"success"`
	Message string `json:"errorMessage,optional"`
}
