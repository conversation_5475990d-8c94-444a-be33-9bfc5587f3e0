syntax = "v1"

info(
    title: "fulfillment 服务接口出入参定义"
    description: "fulfillment 服务api层"
    version: "1.0.0"
)

type (
	GetUserInfoReq {
		Token string `json:"token"`
	}
	GetUserInfoResp {
		Id   string `json:"id"`
		Name string `json:"name"`
	}
	FulfillmentPreference {
		Key                string `json:"key"`
		Region             string `json:"region"`
		Channel            string `json:"channel"`
		WarehouseCode      string `json:"warehouseCode"`
		DisableFulfillment string `json:"disableFulfillment"`
	}
	GetFulfillmentPreferencesReq {
		Region        string `form:"region,optional"`
		Channel       string `form:"channel,optional"`
		WarehouseCode string `form:"warehouseCode,optional"`
	}
	GetFulfillmentPreferencesResp {
		Data []FulfillmentPreference `json:"data"`
	}
	UpdateFulfillmentPreferencesReq {
		Region             string `json:"region"`
		Channel            string `json:"channel"`
		WarehouseCode      string `json:"warehouseCode"`
		DisableFulfillment string `json:"disableFulfillment"`
	}
	RedisQueueMetrics {
		Region      string `json:"region"`
		Channel     string `json:"channel"`
		QueueType   string `json:"queueType"`
		Queue       string `json:"queue"`
		QueueLength int64  `json:"queueLength"`
	}
	GetRedisQueueMetricsReq {
		Region    string `form:"region,optional"`
		Channel   string `form:"channel,optional"`
		QueueType string `form:"queueType,optional"`
		Queue     string `form:"queue,optional"`
	}
	GetRedisQueueMetricsResp {
		Data []RedisQueueMetrics `json:"data"`
	}
	GetOrdersMetricsReq {
		Region string `form:"region,optional"`
		ShopId int64  `form:"shopId,optional"`
		Date   string `form:"date,optional"`
	}
	GetOrdersMetricsResp {
		Data []OrdersMetrics `json:"data"`
	}
	OrdersMetrics {
		Region   string `json:"region"`
		ShopId   int64  `json:"shopId"`
		ShopName string `json:"shopName"`
		Channel  string `json:"channel"`
		Date     string `json:"date"`
		Count    int64  `json:"count"`
	}
	User {
		Id       int64  `json:"id"`
		Region   string `json:"region"`
		Username string `json:"username"`
		Enabled  int64  `json:"enabled"`
	}
	GetUsersReq {
		Region   string `form:"region,optional"`
		Username string `form:"username,optional"`
		Enabled  int64  `form:"enabled,optional"`
	}
	GetUsersResp {
		Data []User `json:"data"`
	}
	CreateUserReq {
		Region   string `json:"region"`
		Username string `json:"username"`
		Password string `json:"password"`
	}
	CreateUserResp {
		Region   string `json:"region"`
		Username string `json:"username"`
	}
	DisableUserReq {
		Id     int64  `path:"id"`
		Region string `json:"region"`
	}
	EnableUserReq {
		Id     int64  `path:"id"`
		Region string `json:"region"`
	}
	DeleteQueueItemReq {
		Region       string `json:"region"`
		Channel      string `path:"channel"`
		ThirdOrderId string `json:"thirdOrderId"`
	}
	DeleteQueueItemResp {
		Region       string `json:"region"`
		Queue        string `json:"queue"`
		Channel      string `json:"channel"`
		ThirdOrderId string `json:"thirdOrderId"`
	}
	GetRedisQueuesReq {
		Region       string `form:"region,optional"`
		Queue        string `form:"queue,optional"`
		Channel      string `form:"channel,optional"`
		ThirdOrderId string `form:"thirdOrderId,optional"`
	}
	RedisQueue {
		Region       string `json:"region"`
		Queue        string `json:"queue"`
		Channel      string `json:"channel"`
		ShopKey      string `json:"shopKey"`
		ThirdOrderId string `json:"thirdOrderId"`
		OrderState   string `json:"orderState"`
	}
	GetRedisQueuesResp {
		Data []RedisQueue `json:"data"`
	}
	PasswordRestReq {
		Id       int64  `path:"id"`
		Region   string `json:"region"`
		Password string `json:"password"`
	}
	Shop {
		Id                 int64  `json:"id,optional"`
		Region             string `json:"region"`
		Platform           string `json:"platform"`
		ShopKey            string `json:"shopKey"`
		ShopName           string `json:"shopName"`
		PlatformShopID     string `json:"platformShopId"`
		Currency           string `json:"currency"`
		AppKey             string `json:"appKey,optional"`
		AppSecret          string `json:"appSecret,optional"`
		AccessToken        string `json:"accessToken,optional"`
		RefreshToken       string `json:"refreshToken,optional"`
		ShopCipher         string `json:"shopCipher,optional"`
		Pro                bool   `json:"pro,optional"`
		MultiWarehouseFlag int    `json:"multiWarehouseFlag,optional"`
		Status             string `json:"status,optional"`
	}
	CreateShopReq {
		Shop
	}
	SyncShopReq {
		Id                 int64  `path:"id"`
		Region             string `json:"region"`
		Platform           string `json:"platform"`
		ShopKey            string `json:"shopKey"`
		ShopName           string `json:"shopName"`
		PlatformShopID     string `json:"platformShopId"`
		Currency           string `json:"currency"`
		AppKey             string `json:"appKey,optional"`
		AppSecret          string `json:"appSecret,optional"`
		AccessToken        string `json:"accessToken,optional"`
		RefreshToken       string `json:"refreshToken,optional"`
		ShopCipher         string `json:"shopCipher,optional"`
		Pro                bool   `json:"pro,optional"`
		MultiWarehouseFlag int    `json:"multiWarehouseFlag,optional"`
	}
	GetShopsReq {
		Region         string `form:"region,optional"`
		ShopKey        string `form:"shopKey,optional"`
		ShopName       string `form:"shopName,optional"`
		Platform       string `form:"platform,optional"`
		PlatformShopID string `form:"platformShopId,optional"`
	}
	UpdateShopReq {
		Id                 int64  `path:"id"`
		ShopName           string `json:"shopName"`
		AppKey             string `json:"appKey"`
		Region             string `json:"region"`
		MultiWarehouseFlag int    `json:"multiWarehouseFlag"`
	}
	GetShopsResp {
		Data []Shop `json:"data"`
	}
	GetThirdOrderReq {
		Id      string `path:"id"`
		Region  string `form:"region"`
		ShopKey string `form:"shopKey"`
	}
	ThirdOrderDetailResp {
		RawData string `json:"rawData"`
	}
	GetThirdStocksReq {
		Region  string `form:"region"`
		ShopKey string `form:"shopKey"`
		Skus    string `form:"skus"`
	}
	ThirdStockItem {
		Sku              string `json:"sku"`
		Channel          string `json:"channel"`
		ChannelProductId string `json:"channelProductId"`
		Quantity         int64  `json:"quantity"`
		RawData          string `json:"rawData"`
	}
	ThirdStocksResp {
		Data []ThirdStockItem `json:"data"`
	}
	GetShopAuthorizationsReq {
		Region   string `form:"region,optional"`
		Platform string `form:"platform,optional"`
		ShopKey  string `form:"shopKey,optional"`
	}
	ShopAuthorization {
		Region         string `json:"region"`
		Platform       string `json:"platform"`
		ShopKey        string `json:"shopKey"`
		ShopName       string `json:"shopName"`
		PlatformShopID string `json:"platformShopId"`
		AccessToken    string `json:"accessToken"`
		RefreshToken   string `json:"refreshToken"`
		ShopCipher     string `json:"shopCipher,optional"`
	}
	GetShopAuthorizationsResp {
		Data []ShopAuthorization `json:"data"`
	}
	CreateShopAuthorizationsReq {
		ShopAuthorization
	}
	CreateShopAuthorizationsResp {
		ShopAuthorization
	}
	GetShopAppsReq {
		Region   string `form:"region,optional"`
		Platform string `form:"platform,optional"`
	}
	ShopApp {
		Region       string            `json:"region"`
		Currency     string            `json:"currency"`
		Platform     string            `json:"platform"`
		AppKey       string            `json:"appKey"`
		AppSecret    string            `json:"appSecret"`
		IsSandbox    bool              `json:"isSandbox"`
		InstallUrls  map[string]string `json:"installUrls"`
		RedirectUrls map[string]string `json:"redirectUrls"`
	}
	GetShopAppsResp {
		Data []ShopApp `json:"data"`
	}
	TourMigrationsListRequest {
		Region string `form:"region,optional"`
	}
	TourMigrationsResponse {
		Region string `json:"region"`
		Status string `json:"status"`
	}
	TourMigrationsListResponse {
		Data []TourMigrationsResponse `json:"data"`
	}
	TourMigrationsUpdateRequest {
		Region string `path:"region"`
		Status string `json:"status"`
	}
	GetScmInventoryItemsReq {
		Skus            string `form:"skus,optional"`
		ProjectCodeList string `form:"projectCodeList,optional"`
		Page            int    `form:"page,optional"`
		PageSize        int    `form:"pageSize,optional"`
		UpdatedAtMin    string `form:"updatedAtMin,optional"`
		UpdatedAtMax    string `form:"updatedAtMax,optional"`
	}
	GetScmInventoryItemsResp {
		RawData string `json:"rawData"`
	}
	GetScmStocksReq {
		Sku           string `form:"skus,optional"`
		WarehouseCode string `form:"warehouseCode,optional"`
		Page          int    `form:"page,optional"`
		PageSize      int    `form:"pageSize,optional"`
	}
	GetScmStocksResp {
		RawData string `json:"rawData"`
	}
	GetScmRequestAuditLogsReq {
		Region  string `form:"region"`
		Keyword string `form:"keyword,optional"`
	}
	ScmRequestAuditLog {
		Id               int64  `json:"id"`
		RequestURL       string `json:"requestUrl"`
		RequestMethod    string `json:"requestMethod"`
		RequestPayload   string `json:"requestPayload"`
		CreatedAt        string `json:"createdAt"`
		ConvertedPayload string `json:"convertedPayload"`
		ResponsePayload  string `json:"responsePayload"`
	}
	GetScmRequestAuditLogsResp {
		Data []ScmRequestAuditLog `json:"data"`
	}
	GetWarehousesReq {
		WarehouseCode         string `form:"warehouseCode,optional"`
		Region                string `form:"region,optional"`
		PhysicalWarehouseCode string `form:"physicalWarehouseCode,optional"`
		LogicalWarehouseCode  string `form:"logicalWarehouseCode,optional"`
	}
	GetWarehousesResp {
		Data []Warehouse `json:"data"`
	}
	Warehouse {
		Id                    int64  `json:"id"`
		Region                string `json:"region"`
		WarehouseCode         string `json:"warehouseCode"`
		Name                  string `json:"name"`
		ThirdCode             string `json:"thirdCode"`
		ActiveFlag            int64  `json:"activeFlag"`
		CreatedAt             string `json:"createdAt"`
		CreatedBy             int64  `json:"createdBy"`
		UpdatedAt             string `json:"updatedAt"`
		UpdatedBy             int64  `json:"updatedBy"`
		FlagDeleted           int64  `json:"flagDeleted"`
		DeletedBy             int64  `json:"deletedBy"`
		DeletedAt             string `json:"deletedAt"`
		DefaultFlag           int64  `json:"defaultFlag"`
		PhysicalWarehouseCode string `json:"physicalWarehouseCode,optional"`
		LogicalWarehouseCode  string `json:"logicalWarehouseCode,optional"`
	}
	UpdateWarehouseReq {
		Id                    int64  `path:"id"`
		Region                string `json:"region"`
		Name                  string `json:"name,optional"`
		ThirdCode             string `json:"thirdCode,optional"`
		PhysicalWarehouseCode string `json:"physicalWarehouseCode,optional"`
		LogicalWarehouseCode  string `json:"logicalWarehouseCode,optional"`
	}
	CreateWarehouseReq {
		Region                string `json:"region"`
		WarehouseCode         string `json:"warehouseCode"`
		Name                  string `json:"name"`
		ThirdCode             string `json:"thirdCode,optional"`
		ActiveFlag            int64  `json:"activeFlag,optional"`
		DefaultFlag           int64  `json:"defaultFlag,optional"`
		PhysicalWarehouseCode string `json:"physicalWarehouseCode"`
		LogicalWarehouseCode  string `json:"logicalWarehouseCode"`
	}
	CreateWarehouseResp {
		Success bool   `json:"success"`
		Error   string `json:"errorMessage,optional"`
	}

	AddShopAppsReq {
		ShopApp
	}
	AddShopAppsResp {
		ShopApp
	}
	PushOrderToScmReq {
		Region        string `json:"region"`
		OrderId       string `json:"orderId"`
		Authorization string `header:"authorization"`
	}
	PushOrderToScmResp {
		Region  string `json:"region"`
		OrderId string `json:"orderId"`
		Success bool   `json:"success"`
		Error   string `json:"error"`
	}
	PushOrderToScmWithoutCancelReq {
		Region        string `json:"region"`
		OrderId       string `json:"orderId"`
		Authorization string `header:"authorization"`
	}
	PushOrderToScmWithoutCancelResp {
		Region  string `json:"region"`
		OrderId string `json:"orderId"`
		Success bool   `json:"success"`
		Error   string `json:"error"`
	}
	GetWmsInboundOrdersReq {
		WarehouseCode string   `json:"warehouseCode"`
		OrderIds      []string `json:"orderIds"`
	}
	GetWmsOutboundOrdersReq {
		WarehouseCode string   `json:"warehouseCode"`
		OrderIds      []string `json:"orderIds"`
	}
	GetWmsOrdersResp {
		RawData string `json:"rawData"`
	}
	GetOrderIdsReq {
		Region    string `form:"region"`
		ShopId    int64  `form:"shopId,optional"`
		StartDate string `form:"startDate"`
		EndDate   string `form:"endDate"`
		State     string `form:"state,optional"`
	}
	GetOrderIdsResp {
		Data []string `json:"data"`
	}
	GetOrdersReq {
		Region    string `form:"region"`
		ShopId    int64  `form:"shopId,optional"`
		Ids       string `form:"ids,optional"`
		StartDate string `form:"startDate"`
		EndDate   string `form:"endDate"`
		State     string `form:"state,optional"`
	}
	GetOrdersResp {
		Data []OrderBase `json:"data"`
	}
	OrderBase {
		ID               string `json:"id"`
		ChannelOrderID   string `json:"channelOrderId"`
		State            string `json:"state"`
		DisplayState     string `json:"displayState"`
		StorePlatform    string `json:"storePlatform"`
		ShopID           int16  `json:"shopId"`
		StoreState       string `json:"storeState"`
		WarehouseID      string `json:"warehouseId"`
		ExpressID        string `json:"expressId"`
		WarehouseState   string `json:"warehouseState"`
		OrderTime        string `json:"orderTime"`
		PayTime          string `json:"payTime"`
		PayWay           string `json:"payWay"`
		OutOfStockReason string `json:"outOfStockReason"`
		ExceptionReason  string `json:"exceptionReason"`
		PreOrder         bool   `json:"preOrder"`
		DeliverFirst     bool   `json:"deliverFirst"`
		Remark           bool   `json:"remark"`
		Hold             bool   `json:"hold"`
		Exception        bool   `json:"exception"`
		Oversold         bool   `json:"oversold"`
		UpdateTime       string `json:"updateTime"`
		WarehouseCode    string `json:"warehouseCode"`
		CustomAttributes string `json:"customAttributes"`
	}
	GetOrderDeliveryCodesReq {
		Region    string `form:"region"`
		ShopId    int64  `form:"shopId,optional"`
		StartDate string `form:"startDate"`
		EndDate   string `form:"endDate"`
	}
	GetOrderDeliveryCodesResp {
		Data []string `json:"data"`
	}
	GetReturnOrderIdsReq {
		Region    string `form:"region"`
		ShopId    int64  `form:"shopId,optional"`
		StartDate string `form:"startDate"`
		EndDate   string `form:"endDate"`
	}
	GetReturnOrderIdsResp {
		Data []string `json:"data"`
	}
	OrderStateMetrics {
		State      string `json:"state"`
		OrderTotal int64  `json:"orderTotal"`
	}
	GetOrderStateMetricsReq {
		Region    string `form:"region"`
		ShopId    int64  `form:"shopId,optional"`
		StartDate string `form:"startDate"`
		EndDate   string `form:"endDate"`
	}
	GetOrderStateMetricsResp {
		Data []OrderStateMetrics `json:"data"`
	}
	OrderShopStateMetrics {
		ShopId     int64  `json:"shopId"`
		ShopName   string `json:"shopName"`
		State      string `json:"state"`
		OrderTotal int64  `json:"orderTotal"`
	}
	GetOrderShopStateMetricsReq {
		Region    string `form:"region"`
		StartDate string `form:"startDate"`
		EndDate   string `form:"endDate"`
	}
	GetOrderShopStateMetricsResp {
		Data []OrderShopStateMetrics `json:"data"`
	}
	GetShopReq {
		Id int64 `path:"id"`
	}
	GetShopResp {
		Data Shop `json:"data"`
	}
	GetProductImageUrlsReq {
		Skus []string `json:"skus"`
	}
	ProductImageUrl {
		Barcode string `json:"barcode"`
		Sku     string `json:"sku"`
		Image1  string `json:"image_1"`
		Image2  string `json:"image_2"`
		Image3  string `json:"image_3"`
		Image4  string `json:"image_4"`
		Image5  string `json:"image_5"`
		Image6  string `json:"image_6"`
	}
	GetProductImageUrlsResp {
		Data []ProductImageUrl `json:"data"`
	}
	FixSkusImagesReq {
		Region string   `json:"region"`
		Skus   []string `json:"skus"`
	}
	FixSkusImagesResp {
		Success bool   `json:"success"`
		Error   string `json:"error,optional"`
	}
	GetOrdersWithoutTrackingReq {
		Region    string `form:"region"`
		StartDate string `form:"startDate"`
		EndDate   string `form:"endDate"`
	}
	GetOrdersWithoutTrackingResp {
		OrderIds   []string `json:"orderIds"`
		OrderCount int64    `json:"orderCount"`
	}
	GetOrdersWithoutDeliveryReq {
		Region    string `form:"region"`
		StartDate string `form:"startDate"`
		EndDate   string `form:"endDate"`
	}
	GetOrdersWithoutDeliveryResp {
		OrderIds   []string `json:"orderIds"`
		OrderCount int64    `json:"orderCount"`
	}
	GetOrdersWithStockIssueReq {
		Region    string `form:"region"`
		StartDate string `form:"startDate"`
		EndDate   string `form:"endDate"`
	}
	GetOrdersWithStockIssueResp {
		OrderIds   []string `json:"orderIds"`
		OrderCount int64    `json:"orderCount"`
	}
	GetOrdersWithCombineSkuIssueReq {
		Region    string `form:"region"`
		StartDate string `form:"startDate"`
		EndDate   string `form:"endDate"`
	}
	GetOrdersWithCombineSkuIssueResp {
		OrderIds   []string `json:"orderIds"`
		OrderCount int64    `json:"orderCount"`
	}
	OrderSyncMetrics {
		TotalOrders               []OrderBase `json:"totalOrders"`
		OrdersWithoutTracking     []OrderBase `json:"ordersWithoutTracking"`
		OrdersWithoutDelivery     []OrderBase `json:"ordersWithoutDelivery"`
		OrdersWithStockIssue      []OrderBase `json:"ordersWithStockIssue"`
		OrdersWithCombineSkuIssue []OrderBase `json:"ordersWithCombineSkuIssue"`
		WmsOrderTotal             []string    `json:"wmsOrderTotal"`
		IsSynchronized            bool        `json:"isSynchronized"`
		MissingOrders             []OrderBase `json:"missingOrders"`
		WmsDocuments              []OrderBase `json:"wmsDocuments"`
	}
	GetOrderSyncMetricsReq {
		Region    string `form:"region"`
		StartDate string `form:"startDate"`
		EndDate   string `form:"endDate"`
	}
	GetOrderSyncMetricsResp {
		Data OrderSyncMetrics `json:"data"`
	}
	InitSearchTermsConfigReq {
		Region          string `json:"region"`
		WarehouseTermId string `json:"warehouseTermId"`
		WarehouseName   string `json:"warehouseName"`
	}
	InitSearchTermsConfigResp {
		Success bool   `json:"success"`
		Error   string `json:"errorMessage,optional"`
	}
	GetFinancialSalesEntityReq {
		PhysicalWarehouseCode string `form:"physical_warehouse_code"`
	}
	GetFinancialSalesEntityResp {
		RawData string `json:"rawData"`
	}
	GetFinancialSalesEntityByOrderIdsReq {
		Region   string   `json:"region"`
		OrderIds []string `json:"orderIds"`
	}
	GetFinancialSalesEntityByOrderIdsResp {
		RawData string `json:"rawData"`
	}
	InitShopProductReq {
		ShopId int64  `path:"id"`
		Region string `json:"region"`
	}
	InitShopProductResp {
		Success bool   `json:"success"`
		Error   string `json:"error,optional"`
	}
	SiteConfig {
		Region        string `json:"region"` // 站点代码，如 TH
		RegionName    string `json:"regionName"` // 站点名称，如 泰国
		Currency      string `json:"currency"` // 货币代码，如 THB
		Warehouse     string `json:"warehouse"` // 仓库代码，如 TLRT_TH_EC_PRO
		WarehouseName string `json:"warehouseName"` // 仓库名称，如 TLRT-泰国-电商-商品仓
		ProvinceId    string `json:"provinceId"` // 省份ID，如 Jawa Barat
		CityId        string `json:"cityId"` // 城市ID，如 Kabupaten Bekasi
		DetailId      string `json:"detailId"` // 详细地址，如 WX4J+6Q7 Warehouse FMCG-A JD.ID Online shoping, Sagara Makmur, Kec. Tarumajaya, Kabupaten Bekasi, Jawa Barat 17211
	}
	GetSiteConfigReq {
		Region string `form:"region,optional"` // 如果不提供，则返回所有站点配置
	}
	GetSiteConfigResp {
		Data []SiteConfig `json:"data"`
	}
	UpdateSiteConfigReq {
		Region        string `json:"region"`
		RegionName    string `json:"regionName"`
		Currency      string `json:"currency"`
		Warehouse     string `json:"warehouse"`
		WarehouseName string `json:"warehouseName"`
		ProvinceId    string `json:"provinceId"`
		CityId        string `json:"cityId"`
		DetailId      string `json:"detailId"`
	}
	UpdateSiteConfigResp {
		Data SiteConfig `json:"data"`
	}
	DeleteSiteConfigReq {
		Region string `path:"region"`
	}
	DeleteSiteConfigResp {
		Success bool   `json:"success"`
		Error   string `json:"error,optional"`
	}
	QueueConsumptionStatus {
		Region   string `json:"region"`
		Platform string `json:"platform"`
		Enabled  string `json:"enabled"`
		RedisKey string `json:"redisKey"`
	}
	GetQueueConsumptionStatusReq {
		Region   string `form:"region,optional"`
		Platform string `form:"platform,optional"`
		Enabled  string `form:"enabled,optional"`
	}
	GetQueueConsumptionStatusResp {
		Data []QueueConsumptionStatus `json:"data"`
	}
	UpdateQueueConsumptionStatusReq {
		Region   string `json:"region"`
		Platform string `json:"platform"`
		Enabled  string `json:"enabled"`
	}
	UpdateQueueConsumptionStatusResp {
		Success bool   `json:"success"`
		Error   string `json:"error,optional"`
	}
	SyncOrderWarehouseStateReq {
		Region   string   `json:"region"`
		OrderIds []string `json:"orderIds"`
	}
	SyncOrderWarehouseStateResp {
		Success bool   `json:"success"`
		Error   string `json:"error,optional"`
	}
	SyncSingleOrderWarehouseStateReq {
		Region   string `path:"region"`
		DeliveryCode  string `path:"deliveryCode"`
	}
	SyncSingleOrderWarehouseStateResp {
		Success bool   `json:"success"`
		Error   string `json:"error,optional"`
	}

	// WarehouseStockSync related types
	WarehouseStockSync {
		Id            int64  `json:"id"`
		Region        string `json:"region"`        // 站点代码，如 TH
		WarehouseCode string `json:"warehouseCode"` // 仓库编码
		NotifyRegion  string `json:"notifyRegion"`  // 通知地区
		CreateTime    string `json:"createTime"`    // 创建时间
		UpdateTime    string `json:"updateTime"`    // 更新时间
	}

	GetWarehouseStockSyncsReq {
		Region        string `form:"region,optional"`
		WarehouseCode string `form:"warehouseCode,optional"`
		NotifyRegion  string `form:"notifyRegion,optional"`
	}

	GetWarehouseStockSyncsResp {
		Data []WarehouseStockSync `json:"data"`
	}

	CreateWarehouseStockSyncReq {
		Region        string `json:"region"`        // 站点代码，如 TH
		WarehouseCode string `json:"warehouseCode"` // 仓库编码
		NotifyRegion  string `json:"notifyRegion"`  // 通知地区
	}

	UpdateWarehouseStockSyncReq {
		Id           int64  `path:"id"`
		Region       string `path:"region"`       // 站点代码，如 TH
		NotifyRegion string `json:"notifyRegion"` // 通知地区
	}

	DeleteWarehouseStockSyncReq {
		Region string `json:"region"`
		Id int64 `path:"id"`
	}
	SyncShopOrderWithChannelReq {
		Region string `path:"region"`
		ShopId string `path:"shop_id"`
		OrderId string `path:"order_id"`
	}
	SyncShopOrderWithChannelResp {
		Success bool `json:"success"`
		Error string `json:"errorMessage,optional"`
	}
	PatchShopStatusReq {
		Region string `path:"region"`
		ShopId int64 `path:"id"`
		Status string `json:"status"`
	}
	PatchShopStatusResp {
		Success bool `json:"success"`
		Error string `json:"errorMessage,optional"`
	}
	GetThirdProductsReq {
		Region string `path:"region"`
		ShopId int64 `path:"shop_id"`
		Page          int    `form:"page,optional"`
		PageSize      int    `form:"pageSize,optional"`
	}
	GetThirdProductsResp {
		Data []ThirdProduct `json:"data"`
		// hardcode total 10000
		Total int64 `json:"total"`
	}
	ThirdProduct {
		Id string `json:"id"`
		ShopId string `json:"shop_id"`
		ProductId string `json:"product_id"`
		ProductName string `json:"product_name"`
		ProductSku string `json:"product_sku"`
		ProductStatus string `json:"product_status"`
	}
	// 退货落回库 Return Merchandise
	ReturnMerchandiseBackWarehouseReq {
		Region        string `path:"region"`
		WarehouseInNo string `path:"warehouse_in_no"`
	}
	ReturnMerchandiseBackWarehouseResp {
		Success bool   `json:"success"`
	}
	// 更新 deliver_item 销售交易主体维护接口
	UpdateDeliverItemSalesTransactionReq {
		RelOrderNo     string `json:"relOrderNo"`
		Sku            string `json:"sku"`
		SubjectCode    string `json:"subjectCode"`
		ChangeQuantity int    `json:"changeQuantity"`
		CreateTime     string `json:"createTime"`
	}
	UpdateDeliverItemSalesTransactionResp {
		Success bool   `json:"success"`
		Message string `json:"message"`
	}
)

@server (
	middleware: AuthInterceptor
	prefix:     /api
	group:      fulfillment_center
)
service webapi {
	@handler getUserInfo
	get /user/info returns (GetUserInfoResp)

	@handler getFulfillmentPreferences
	get /fulfillment/preferences (GetFulfillmentPreferencesReq) returns (GetFulfillmentPreferencesResp)

	@handler updateFulfillmentPreferences
	put /fulfillment/preferences (UpdateFulfillmentPreferencesReq) returns (FulfillmentPreference)

	@handler getTourMigrations
	get /tour-migrations (TourMigrationsListRequest) returns (TourMigrationsListResponse)

	@handler updateTourMigrations
	put /tour-migrations/:region (TourMigrationsUpdateRequest) returns (TourMigrationsResponse)

	@handler getScmInventoryItems
	get /scm/inventory-items (GetScmInventoryItemsReq) returns (GetScmInventoryItemsResp)

	@handler getScmStocks
	get /scm/stocks (GetScmStocksReq) returns (GetScmStocksResp)

	@handler getScmRequestAuditLogs
	get /scm/request-audit-logs (GetScmRequestAuditLogsReq) returns (GetScmRequestAuditLogsResp)

	@handler PushOrderToScm
	post /scm/push-order (PushOrderToScmReq) returns (PushOrderToScmResp)

	@handler PushOrderToScmWithoutCancel
	post /scm/push-order-without-cancel (PushOrderToScmWithoutCancelReq) returns (PushOrderToScmWithoutCancelResp)

	@handler getWarehouses
	get /warehouses (GetWarehousesReq) returns (GetWarehousesResp)

	@handler createWarehouse
	post /warehouses (CreateWarehouseReq) returns (CreateWarehouseResp)

	@handler updateWarehouse
	put /warehouses/:id (UpdateWarehouseReq) returns (Warehouse)

	@handler getRedisMetrics
	get /metrics/redis-queue (GetRedisQueueMetricsReq) returns (GetRedisQueueMetricsResp)

	@handler getOrdersMetrics
	get /metrics/orders (GetOrdersMetricsReq) returns (GetOrdersMetricsResp)

	@handler getUsers
	get /users (GetUsersReq) returns (GetUsersResp)

	@handler createUser
	post /users (CreateUserReq) returns (CreateUserResp)

	@handler disableUser
	post /users/:id/disable (DisableUserReq) returns (User)

	@handler resetPassword
	post /users/:id/password (PasswordRestReq) returns (User)

	@handler enableUser
	post /users/:id/enable (EnableUserReq) returns (User)

	@handler getRedisQueues
	get /maintenance/redis-queues (GetRedisQueuesReq) returns (GetRedisQueuesResp)

	@handler deleteQueueItem
	delete /maintenance/redis-queues/:channel/item (DeleteQueueItemReq) returns (DeleteQueueItemResp)

	@handler getShops
	get /shops (GetShopsReq) returns (GetShopsResp)

	@handler createShop
	post /shops (CreateShopReq) returns (Shop)

	@handler syncShop
	post /shops/:id/sync (SyncShopReq) returns (Shop)

	@handler updateShop
	put /shops/:id (UpdateShopReq) returns (Shop)

	// patch shops/:id/status
	@handler patchShopStatus
	patch /regions/:region/shops/:id/status (PatchShopStatusReq) returns (PatchShopStatusResp)

	@handler getTikTokOrderDetail
	get /tiktok-orders/:id (GetThirdOrderReq) returns (ThirdOrderDetailResp)

	@handler getTikTokStocks
	get /tiktok-stocks (GetThirdStocksReq) returns (ThirdStocksResp)

	@handler getShopeeOrderDetail
	get /shopee-orders/:id (GetThirdOrderReq) returns (ThirdOrderDetailResp)

	@handler getShopeeStocks
	get /shopee-stocks (GetThirdStocksReq) returns (ThirdStocksResp)

	@handler getLazadaOrderDetail
	get /lazada-orders/:id (GetThirdOrderReq) returns (ThirdOrderDetailResp)

	@handler getLazadaStocks
	get /lazada-stocks (GetThirdStocksReq) returns (ThirdStocksResp)

	@handler getTokopediaOrderDetail
	get /tokopedia-orders/:id (GetThirdOrderReq) returns (ThirdOrderDetailResp)

	@handler getTokopediaStocks
	get /tokopedia-stocks (GetThirdStocksReq) returns (ThirdStocksResp)

	@handler getShoplineOrderDetail
	get /shopline-orders/:id (GetThirdOrderReq) returns (ThirdOrderDetailResp)

	@handler getShoplineStocks
	get /shopline-stocks (GetThirdStocksReq) returns (ThirdStocksResp)

	@handler getShopeeProducts
	get /region/:region/shopee/:shop_id/products (GetThirdProductsReq) returns (GetThirdProductsResp)

	@handler getLazadaProducts
	get /region/:region/lazada/:shop_id/products (GetThirdProductsReq) returns (GetThirdProductsResp)

	@handler getTikTokProducts
	get /region/:region/tiktok/:shop_id/products (GetThirdProductsReq) returns (GetThirdProductsResp)

	@handler getShopAuthorizations
	get /shop-authorizations (GetShopAuthorizationsReq) returns (GetShopAuthorizationsResp)

	@handler createShopAuthorizations
	post /shop-authorizations (CreateShopAuthorizationsReq) returns (CreateShopAuthorizationsResp)

	@handler getShopApps
	get /shop-apps (GetShopAppsReq) returns (GetShopAppsResp)

	@handler addShopApps
	post /shop-apps (AddShopAppsReq) returns (AddShopAppsResp)

	@handler getWmsInboundOrders
	post /wms/inbound-orders (GetWmsInboundOrdersReq) returns (GetWmsOrdersResp)

	@handler getWmsOutboundOrders
	post /wms/outbound-orders (GetWmsOutboundOrdersReq) returns (GetWmsOrdersResp)

	@handler getOrderIds
	get /orders/ids (GetOrderIdsReq) returns (GetOrderIdsResp)

	@handler getOrders
	get /orders (GetOrdersReq) returns (GetOrdersResp)

	@handler getOrderDeliveryCodes
	get /orders/delivery-codes (GetOrderDeliveryCodesReq) returns (GetOrderDeliveryCodesResp)

	@handler getReturnOrderIds
	get /orders/return-ids (GetReturnOrderIdsReq) returns (GetReturnOrderIdsResp)

	@handler getOrderStateMetrics
	get /orders/state-metrics (GetOrderStateMetricsReq) returns (GetOrderStateMetricsResp)

	@handler getOrderShopStateMetrics
	get /orders/shop-state-metrics (GetOrderShopStateMetricsReq) returns (GetOrderShopStateMetricsResp)

	@handler getShop
	get /shops/:id (GetShopReq) returns (GetShopResp)

	@handler getProductImageUrls
	post /products/image-urls (GetProductImageUrlsReq) returns (GetProductImageUrlsResp)

	@handler fixSkusImages
	post /maintenance/fix-skus-images (FixSkusImagesReq) returns (FixSkusImagesResp)

	@handler getOrdersWithoutTracking
	get /orders/without-tracking (GetOrdersWithoutTrackingReq) returns (GetOrdersWithoutTrackingResp)

	@handler getOrdersWithoutDelivery
	get /orders/without-delivery (GetOrdersWithoutDeliveryReq) returns (GetOrdersWithoutDeliveryResp)

	@handler getOrdersWithStockIssue
	get /orders/with-stock-issue (GetOrdersWithStockIssueReq) returns (GetOrdersWithStockIssueResp)

	@handler getOrdersWithCombineSkuIssue
	get /orders/with-combine-sku-issue (GetOrdersWithCombineSkuIssueReq) returns (GetOrdersWithCombineSkuIssueResp)

	@handler getOrderSyncMetrics
	get /orders/sync-metrics (GetOrderSyncMetricsReq) returns (GetOrderSyncMetricsResp)

	@handler initSearchTermsConfig
	post /maintenance/init-search-terms-config (InitSearchTermsConfigReq) returns (InitSearchTermsConfigResp)

	@handler getFinancialSalesEntity
	get /financial/sales-entity (GetFinancialSalesEntityReq) returns (GetFinancialSalesEntityResp)

	@handler getFinancialSalesEntityByOrderIds
	post /financial/sales-entity-by-order-ids (GetFinancialSalesEntityByOrderIdsReq) returns (GetFinancialSalesEntityByOrderIdsResp)

	@handler initShopProduct
	post /shops/:id/init-product (InitShopProductReq) returns (InitShopProductResp)

	@handler getSiteConfig
	get /site-config (GetSiteConfigReq) returns (GetSiteConfigResp)

	@handler updateSiteConfig
	put /site-config (UpdateSiteConfigReq) returns (UpdateSiteConfigResp)

	@handler deleteSiteConfig
	delete /site-config/:region (DeleteSiteConfigReq) returns (DeleteSiteConfigResp)

	@handler getQueueConsumptionStatus
	get /maintenance/queue-consumption (GetQueueConsumptionStatusReq) returns (GetQueueConsumptionStatusResp)

	@handler updateQueueConsumptionStatus
	put /maintenance/queue-consumption (UpdateQueueConsumptionStatusReq) returns (UpdateQueueConsumptionStatusResp)

	@handler syncOrderWarehouseState
	post /maintenance/sync-order-warehouse-state (SyncOrderWarehouseStateReq) returns (SyncOrderWarehouseStateResp)

	@handler syncSingleOrderWarehouseState
	post /maintenance/:region/orders/:deliveryCode/sync-warehouse-state (SyncSingleOrderWarehouseStateReq) returns (SyncSingleOrderWarehouseStateResp)

	// POST /maintenance/:region/shops/{shop_id}/orders/{order_id}/sync
	@handler syncShopOrderWithChannel
	post /maintenance/:region/shops/:shop_id/orders/:order_id/sync (SyncShopOrderWithChannelReq) returns (SyncShopOrderWithChannelResp)


	// WarehouseStockSync APIs
	@handler getWarehouseStockSyncs
	get /warehouse-stock-syncs (GetWarehouseStockSyncsReq) returns (GetWarehouseStockSyncsResp)

	@handler createWarehouseStockSync
	post /warehouse-stock-syncs (CreateWarehouseStockSyncReq) returns (WarehouseStockSync)

	@handler updateWarehouseStockSync
	put /regions/:region/warehouse-stock-syncs/:id (UpdateWarehouseStockSyncReq) returns (WarehouseStockSync)

	@handler deleteWarehouseStockSync
	delete /regions/:region/warehouse-stock-syncs/:id (DeleteWarehouseStockSyncReq) returns (WarehouseStockSync)

	@handler returnMerchandiseBackWarehouse
	patch /regions/:region/return-request-migration/:warehouse_in_no (ReturnMerchandiseBackWarehouseReq) returns(ReturnMerchandiseBackWarehouseResp)

	// 更新 deliver_item 销售交易主体维护接口
	@handler updateDeliverItemSalesTransaction
	post /maintenance/deliver-item/sales-transaction (UpdateDeliverItemSalesTransactionReq) returns (UpdateDeliverItemSalesTransactionResp)
}
