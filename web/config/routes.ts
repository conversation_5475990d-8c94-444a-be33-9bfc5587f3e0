﻿/**
 * @name umi 的路由配置
 * @description 只支持 path,component,routes,redirect,wrappers,name,icon 的配置
 * @param path  path 只支持两种占位符配置，第一种是动态参数 :id 的形式，第二种是 * 通配符，通配符只能出现路由字符串的最后。
 * @param component 配置 location 和 path 匹配后用于渲染的 React 组件路径。可以是绝对路径，也可以是相对路径，如果是相对路径，会从 src/pages 开始找起。
 * @param routes 配置子路由，通常在需要为多个路径增加 layout 组件时使用。
 * @param redirect 配置路由跳转
 * @param wrappers 配置路由组件的包装组件，通过包装组件可以为当前的路由组件组合进更多的功能。 比如，可以用于路由级别的权限校验
 * @param name 配置路由的标题，默认读取国际化文件 menu.ts 中 menu.xxxx 的值，如配置 name 为 login，则读取 menu.ts 中 menu.login 的取值作为标题
 * @param icon 配置路由的图标，取值参考 https://ant.design/components/icon-cn， 注意去除风格后缀和大小写，如想要配置图标为 <StepBackwardOutlined /> 则取值应为 stepBackward 或 StepBackward，如想要配置图标为 <UserOutlined /> 则取值应为 user 或者 User
 * @doc https://umijs.org/docs/guides/routes
 */
export default [
  {
    path: '/',
    redirect: '/dashboard',
  },
  {
    path: '/dashboard',
    name: '系统导航',
    icon: 'DashboardOutlined',
    component: './Dashboard/Index',
  },
  {
    path: '/user',
    layout: false,
    routes: [
      {
        name: 'login',
        path: '/user/login',
        component: './FulfillmentCenter/User/Login',
      },
    ],
  },
  {
    path: '/fulfillment-center',
    name: '履约中心',
    icon: 'ClusterOutlined',
    routes: [
      {
        path: '/fulfillment-center',
        redirect: '/fulfillment-center/overview',
      },
      {
        path: '/fulfillment-center/overview',
        name: '总览',
        icon: 'DashboardOutlined',
        component: './FulfillmentCenter/Metrics',
      },
      {
        path: '/fulfillment-center/orders',
        name: 'Orders',
        icon: 'ShoppingCartOutlined',
        routes: [
          {
            path: '/fulfillment-center/orders',
            redirect: '/fulfillment-center/orders/list',
          },
          {
            name: 'List',
            path: '/fulfillment-center/orders/list',
            component: './FulfillmentCenter/Order/OrderList',
          },
          {
            name: 'Metrics',
            path: '/fulfillment-center/orders/metrics',
            component: './FulfillmentCenter/Order/Metrics',
          },
          {
            name: 'State Metrics',
            path: '/fulfillment-center/orders/state-metrics',
            component: './FulfillmentCenter/Order/StateMetrics',
          },
          {
            name: 'Sync Metrics',
            path: '/fulfillment-center/orders/sync-metrics',
            component: './FulfillmentCenter/Order/SyncMetrics',
          }
        ]
      },
      {
        path: '/fulfillment-center/shop',
        name: 'Shop',
        icon: 'ShopOutlined',
        routes: [
          {
            path: '/fulfillment-center/shop',
            redirect: '/fulfillment-center/shop/list',
          },
          {
            name: 'Lists',
            path: '/fulfillment-center/shop/list',
            component: './FulfillmentCenter/Shop/Lists',
          },
          {
            name: 'Apps',
            path: '/fulfillment-center/shop/apps',
            component: './FulfillmentCenter/Shop/Apps',
          },
          {
            name: 'Channel Order Info',
            path: '/fulfillment-center/shop/channel-order-info',
            component: './FulfillmentCenter/Shop/ChannelOrderInfo',
          },
          {
            name: 'Channel Stock Info',
            path: '/fulfillment-center/shop/channel-stock-info',
            component: './FulfillmentCenter/Shop/ChannelStockInfo',
          },
          {
            name: 'TikTok Products',
            path: '/fulfillment-center/shop/tiktok-products',
            component: './FulfillmentCenter/Shop/TikTokProducts',
          },
          {
            name: 'Shopee Products',
            path: '/fulfillment-center/shop/shopee-products',
            component: './FulfillmentCenter/Shop/ShopeeProducts',
          },
          {
            name: 'Lazada Products',
            path: '/fulfillment-center/shop/lazada-products',
            component: './FulfillmentCenter/Shop/LazadaProducts',
          }
        ]
      },
      {
        path: '/fulfillment-center/wms',
        name: 'WMS',
        icon: 'InboxOutlined',
        routes: [
          {
            path: '/fulfillment-center/wms',
            redirect: '/fulfillment-center/wms/inbound-orders',
          },
          {
            name: 'Inbound Orders',
            path: '/fulfillment-center/wms/inbound-orders',
            component: './FulfillmentCenter/WMS/InboundOrders',
          },
          {
            name: 'Outbound Orders',
            path: '/fulfillment-center/wms/outbound-orders',
            component: './FulfillmentCenter/WMS/OutboundOrders',
          }
        ]
      },
      {
        path: '/fulfillment-center/scm',
        name: 'SCM',
        icon: 'ApiOutlined',
        routes: [
          {
            path: '/fulfillment-center/scm',
            redirect: '/fulfillment-center/scm/inventory-items',
          },
          {
            name: 'Inventory Items',
            path: '/fulfillment-center/scm/inventory-items',
            component: './FulfillmentCenter/SCM/InventoryItems',
          },
          {
            name: 'Stocks',
            path: '/fulfillment-center/scm/stocks',
            component: './FulfillmentCenter/SCM/Stocks',
          },
          {
            name: 'Push Order',
            path: '/fulfillment-center/scm/push-order',
            component: './FulfillmentCenter/SCM/PushOrder',
          },
          {
            name: 'Request Audit Logs',
            path: '/fulfillment-center/scm/request-audit-logs',
            component: './FulfillmentCenter/SCM/RequestAuditLogs',
          }
        ]
      },
      {
        path: '/fulfillment-center/fulfillment',
        name: 'Fulfillment',
        icon: 'table',
        routes: [
          {
            path: '/fulfillment-center/fulfillment',
            redirect: '/fulfillment-center/fulfillment/preference',
          },
          {
            name: 'Preference',
            path: '/fulfillment-center/fulfillment/preference',
            component: './FulfillmentCenter/Fulfillment/Preference',
          }
        ]
      },
      {
        path: '/fulfillment-center/warehouse',
        name: 'Warehouse',
        icon: 'BankOutlined',
        routes: [
          {
            path: '/fulfillment-center/warehouse',
            redirect: '/fulfillment-center/warehouse/list',
          },
          {
            name: 'List',
            path: '/fulfillment-center/warehouse/list',
            component: './FulfillmentCenter/Warehouse/List',
          },
          {
            name: 'Stock Sync',
            path: '/fulfillment-center/warehouse/stock-sync',
            component: './FulfillmentCenter/Warehouse/StockSync',
          },
        ]
      },
      {
        path: '/fulfillment-center/product',
        name: 'Product',
        icon: 'PictureOutlined',
        routes: [
          {
            path: '/fulfillment-center/product',
            redirect: '/fulfillment-center/product/images',
          },
          {
            name: 'Images',
            path: '/fulfillment-center/product/images',
            component: './FulfillmentCenter/Product/Images',
          }
        ]
      },
      {
        path: '/fulfillment-center/maintenance',
        name: 'Maintenance',
        icon: 'ControlOutlined',
        routes: [
          {
            path: '/fulfillment-center/maintenance',
            redirect: '/fulfillment-center/maintenance/redis-queues',
          },
          {
            name: 'Redis Queues',
            path: '/fulfillment-center/maintenance/redis-queues',
            component: './FulfillmentCenter/Maintenance/RedisQueue/List',
          },
          {
            name: 'Queue Status',
            path: '/fulfillment-center/maintenance/queue-status',
            component: './FulfillmentCenter/Maintenance/QueueStatus',
          },
          {
            name: 'Fix SKU Images',
            path: '/fulfillment-center/maintenance/fix-skus-images',
            component: './FulfillmentCenter/Maintenance/FixSkusImages',
          },
          {
            name: 'Migration switch',
            path: '/fulfillment-center/maintenance/migration-switch',
            component: './FulfillmentCenter/Tour/Migrations',
          },
          {
            name: 'Init Search Terms',
            path: '/fulfillment-center/maintenance/init-search-terms',
            component: './FulfillmentCenter/Maintenance/InitSearchTerms',
          },
          {
            name: 'Site Config',
            path: '/fulfillment-center/maintenance/site-config',
            component: './FulfillmentCenter/Maintenance/SiteConfig',
          },
          {
            path: '/fulfillment-center/maintenance/sync-order-warehouse-state',
            name: 'Sync Order Warehouse State',
            component: './FulfillmentCenter/Maintenance/SyncOrderWarehouseState',
          },
          {
            path: '/fulfillment-center/maintenance/sync-shop-order-with-channel',
            name: 'Sync Shop Order With Channel',
            component: './FulfillmentCenter/Maintenance/SyncShopOrderWithChannel',
          },
          {
            path: '/fulfillment-center/maintenance/return-merchandise-back-warehouse',
            name: 'WarehouseIn Record Migration',
            component: './FulfillmentCenter/Maintenance/ReturnMerchandiseBackWarehouse',
          },
          {
            path: '/fulfillment-center/maintenance/update-deliver-item-sales-transaction',
            name: 'Update DeliverItem SalesTransaction',
            component: './FulfillmentCenter/Maintenance/DeliverItemSalesTransaction',
          },
        ]
      },

      {
        path: '/fulfillment-center/users',
        name: 'Users',
        icon: 'UserOutlined',
        component: './FulfillmentCenter/User/Lists',
      },
      {
        path: '/fulfillment-center/financial',
        name: 'Financial',
        icon: 'AccountBookOutlined',
        routes: [
          {
            path: '/fulfillment-center/financial',
            redirect: '/fulfillment-center/financial/sales-entity',
          },
          {
            name: 'Sales Entity',
            path: '/fulfillment-center/financial/sales-entity',
            component: './FulfillmentCenter/Financial/SalesEntity',
          },
          {
            name: 'Sales Entity By Orders',
            path: '/fulfillment-center/financial/sales-entity-by-orders',
            component: './FulfillmentCenter/Financial/SalesEntityByOrders',
          }
        ]
      }
    ]
  },
  {
    icon: 'TranslationOutlined',
    path: '/i18n',
    name: '国际化翻译',
    routes: [
      {
        path: '/i18n',
        redirect: '/i18n/translations/list',
      },
      {
        path: '/i18n/translations',
        redirect: '/i18n/translations/list',
      },
      {
        path: '/i18n/translations/list',
        name: 'Translation List',
        component: './I18n/Translation/Lists',
      },
      {
        path: '/i18n/translations/values',
        name: 'Translation Values',
        component: './I18n/Translation/Values',
      },
    ],
  },
  // 数据分析中心 analysis
  {
    path: '/analysis',
    name: '数据分析中心',
    icon: 'BarChartOutlined',
    routes: [
      {
        path: '/analysis',
        redirect: '/analysis/influencer/videos',
      },
      {
        path: '/analysis/influencer',
        icon: 'TeamOutlined',
        name: 'Influencer',
        routes: [
          {
            icon: 'VideoCameraOutlined',
            path: '/analysis/influencer/videos',
            name: 'Videos',
            component: './Analysis/InfluencerVideos/Lists',
          },
          {
            icon: 'LineChartOutlined',
            path: '/analysis/influencer/video-metrics',
            name: 'Video Metrics',
            component: './Analysis/InfluencerVideoMetrics/Lists',
          },
          {
            icon: 'LineChartOutlined',
            path: '/analysis/influencer/gmv-summary',
            name: 'GMV Summary',
            component: './Analysis/InfluencerGmvSummary/Lists',
          },
          {
            icon: 'DatabaseOutlined',
            path: '/analysis/influencer/video-datasources',
            name: 'Video Datasources',
            component: './Analysis/InfluencerVideoDatasources/Lists',
          },
          {
            icon: 'TagsOutlined',
            path: '/analysis/influencer/product-references',
            name: 'Product References',
            component: './Analysis/InfluencerProductReferences/Lists',
          },
        ],
      },
      {
        path: '/analysis/live-performance',
        icon: 'PlayCircleOutlined',
        name: 'Live Performance',
        routes: [
          {
            path: '/analysis/live-performance/lists',
            name: 'Lives',
            component: './Analysis/LivePerformance/Lists',
          },
          {
            icon: 'DatabaseOutlined',
            path: '/analysis/live-performance/datasources',
            name: 'Datasources',
            component: './Analysis/LivePerformance/Datasources',
          },
          {
            icon: 'TargetOutlined',
            path: '/analysis/live-performance/goals',
            name: 'Goals',
            component: './Analysis/LivePerformance/Goals',
          },
        ],
      },
    ]

  },
  // release management
  {
    icon: 'DeploymentUnitOutlined',
    path: '/release',
    name: '发布管理',
    routes: [
      {
        path: '/release',
        redirect: '/release/plans/list',
      },
      {
        path: '/release/applications',
        icon: 'AppstoreOutlined',
        name: 'Applications',
        routes: [
          {
            path: '/release/applications/list',
            name: 'Application List',
            component: './Release/Application/List',
          },
          {
            path: '/release/applications/create',
            name: 'Create Application',
            component: './Release/Application/Create',
          },
          {
            path: '/release/applications/edit/:id',
            name: 'Edit Application',
            component: './Release/Application/Create',
            hideInMenu: true,
          },
        ],
      },
      {
        path: '/release/plans',
        icon: 'ProjectOutlined',
        name: 'Release Plans',
        routes: [
          {
            path: '/release/plans/list',
            name: 'Plan List',
            component: './Release/ReleasePlans',
          },
          {
            path: '/release/plans/create',
            name: 'Create Plan',
            component: './Release/CreateReleasePlan',
            hideInMenu: true,
          },
          {
            path: '/release/plans/:id',
            name: 'Plan Detail',
            component: './Release/ReleasePlanDetail',
            hideInMenu: true,
          },
        ],
      },
      {
        path: '/release/config-compare',
        icon: 'DiffOutlined',
        name: '配置文件对比',
        component: './Release/ConfigCompare',
      },
    ]

  },
  // ACL 权限管理
  {
    path: '/acl',
    name: '权限管理',
    icon: 'SafetyCertificateOutlined',
    routes: [
      {
        path: '/acl',
        redirect: '/acl/config',
      },
      {
        path: '/acl/config',
        name: 'ACL 配置',
        component: './Acl/Config',
      },
      {
        path: '/acl/roles',
        name: '角色管理',
        component: './Acl/Roles',
      },
      {
        path: '/acl/permissions',
        name: '权限管理',
        component: './Acl/Permissions',
      },
      {
        path: '/acl/users',
        name: '用户管理',
        component: './Acl/User/Lists',
      },
    ],
  },
  // Team related routes
  {
    path: '/team',
    name: '团队管理',
    icon: 'TeamOutlined',
    routes: [
      {
        path: '/team',
        redirect: '/team/onduty',
      },
      {
        path: '/team/onduty',
        name: '值班管理',
        icon: 'CalendarOutlined',
        routes: [
          {
            path: '/team/onduty',
            redirect: '/team/onduty/calendar',
          },
          {
            path: '/team/onduty/calendar',
            name: '值班日历',
            component: './Team/Onduty',
          },
          {
            path: '/team/onduty/pairs',
            name: '值班对管理',
            component: './Team/Onduty/Pairs',
          },
          {
            path: '/team/onduty/search',
            name: '值班查询',
            component: './Team/Onduty/Search',
          },
        ],
      },
    ],
  },
  // 审批管理
  {
    path: '/approval',
    name: '审批管理',
    icon: 'AuditOutlined',
    routes: [
      {
        path: '/approval',
        redirect: '/approval/flows',
      },
      {
        path: '/approval/flows',
        name: '审批流程',
        icon: 'NodeIndexOutlined',
        routes: [
          {
            path: '/approval/flows',
            redirect: '/approval/flows/list',
          },
          {
            path: '/approval/flows/list',
            name: '流程列表',
            component: './Approval/Flows/List',
          },
          {
            path: '/approval/flows/create',
            name: '创建流程',
            component: './Approval/Flows/Create',
            hideInMenu: true,
          },
          {
            path: '/approval/flows/:id/edit',
            name: '编辑流程',
            component: './Approval/Flows/Edit',
            hideInMenu: true,
          },
        ],
      },
      {
        path: '/approval/tasks',
        name: '审批任务',
        icon: 'CheckSquareOutlined',
        routes: [
          {
            path: '/approval/tasks',
            redirect: '/approval/tasks/me',
          },
          {
            path: '/approval/tasks/me',
            name: '我的任务',
            component: './Approval/Tasks/MyTasks',
          },
          {
            path: '/approval/tasks/all',
            name: '全部任务',
            component: './Approval/Tasks/AllTasks',
          },
          {
            path: '/approval/tasks/:id',
            name: '任务详情',
            component: './Approval/Tasks/Detail',
            hideInMenu: true,
          },
        ],
      },
      {
        path: '/approval/instances/:id',
        name: '审批实例详情',
        component: './Approval/Instances/Detail',
        hideInMenu: true,
      },
    ],
  },
  // 运维管理
  {
    path: '/ops',
    name: '运维管理',
    icon: 'ToolOutlined',
    routes: [
      {
        path: '/ops',
        redirect: '/ops/xxl-job',
      },
      {
        path: '/ops/xxl-job',
        name: 'XXL任务管理',
        icon: 'ScheduleOutlined',
        routes: [
          {
            path: '/ops/xxl-job',
            redirect: '/ops/xxl-job/executors',
          },
          {
            path: '/ops/xxl-job/executors',
            name: '执行器管理',
            component: './Ops/XxlJob/Executors',
          },
        ],
      },
      {
        path: '/ops/dms',
        name: 'Database',
        icon: 'database',
        routes: [
          {
            path: '/ops/dms/query',
            name: 'Query',
            component: './Ops/DMS/Query',
          },
          {
            path: '/ops/dms/mutation',
            name: 'Mutation',
            component: './Ops/DMS/Mutation',
            access: 'canAccessDMSMutation',
          },
        ],
      },
      {
        path: '/ops/app-upload',
        name: 'APK 上传',
        icon: 'CloudUploadOutlined',
        component: './Ops/AppUpload',
      },
      {
        path: '/ops/image-processor',
        name: '图片去Logo',
        icon: 'PictureOutlined',
        component: './Ops/ImageProcessor',
      },
      {
        path: '/ops/o2o',
        name: 'O2O 管理',
        icon: 'ShopOutlined',
        routes: [
          {
            path: '/ops/o2o',
            redirect: '/ops/o2o/grab',
          },
          {
            path: '/ops/o2o/grab',
            name: 'Grab 配置',
            icon: 'SettingOutlined',
            component: './Ops/O2O/Grab/Config',
          },
        ],
      },
      {
        path: '/ops/rocketmq',
        name: 'RocketMQ 管理',
        icon: 'MessageOutlined',
        component: './Ops/RocketMQ',
      },
      {
        path: '/ops/feature-control',
        name: '特性开关管理',
        icon: 'ControlOutlined',
        component: './Ops/FeatureControl',
      },
      {
        path: '/ops/ohsome',
        name: 'Ohsome 管理',
        icon: 'ShoppingCartOutlined',
        routes: [
          {
            path: '/ops/ohsome',
            redirect: '/ops/ohsome/funifun-reward-items',
          },
          {
            path: '/ops/ohsome/funifun-reward-items',
            name: 'Funifun奖励商品',
            icon: 'GiftOutlined',
            component: './Ohsome/FunifunRewardItems',
          },
        ],
      },
    ],
  },
  {
    path: '*',
    layout: false,
    component: './404',
  },
];
