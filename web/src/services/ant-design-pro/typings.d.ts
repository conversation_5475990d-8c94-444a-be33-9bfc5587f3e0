// @ts-ignore
/* eslint-disable */

declare namespace API {
  type CurrentUser = {
    id?: string;
    name?: string;
    region?: string;
  };

  type LoginResult = {
    id?: string;
    name?: string;
    token?: string;
  };

  type PageParams = {
    current?: number;
    pageSize?: number;
  };

  type GetOrderMetricsResp = {
    data: OrderMetrics[]
  }

  type OrderMetrics = {
    region?: string;
    channel?: string;
    date?: string;
    shopId?: number;
    shopName?: string;
    count?: number;
  }

  type OrderStateMetrics = {
    state: string;
    orderTotal: number;
  }

  type OrderShopStateMetrics = {
    shopId: number;
    shopName: string;
    state: string;
    orderTotal: number;
  }

  type GetRedisQueueMetricsResp = {
    data: RedisQueueMetrics[]
  }

  // Approval 相关类型定义

  // 审批流程模板相关
  type ApprovalFlow = {
    id: number;
    name: string;
    description: string;
    enabled: boolean;
    created_at: string;
    updated_at: string;
    steps?: ApprovalFlowNode[];
  };

  type ApprovalFlowNode = {
    id: number;
    flow_id: number;
    level: number;
    node_type: string; // single/and/or
    approvers: string[]; // 审批人邮箱列表
    optional: boolean;
    description: string;
    created_at: string;
  };

  type CreateApprovalFlowReq = {
    name: string;
    description: string;
    enabled: boolean;
    steps: CreateApprovalFlowStep[];
  };

  type CreateApprovalFlowStep = {
    level: number;
    node_type: string; // single/and/or
    approvers: string[]; // 审批人邮箱列表
    optional: boolean;
    description: string;
  };

  type GetApprovalFlowsReq = {
    enabled?: string;
    page?: number;
    limit?: number;
  };

  type GetApprovalFlowsResp = {
    list: ApprovalFlow[];
    total: number;
  };

  type UpdateApprovalFlowReq = {
    id: number;
    name: string;
    description: string;
    enabled: boolean;
    steps: CreateApprovalFlowStep[];
  };

  // 发布申请相关
  type CreateReleaseRequestReq = {
    title: string;
    branch: string;
    environment: string;
    description: string;
    approval_flow_id: number;
  };

  type ReleaseRequest = {
    id: string;
    title: string;
    branch: string;
    environment: string;
    description: string;
    status: string;
    applicant_email: string;
    approval_flow_id: number;
    approval_instance_id: string;
    created_at: string;
    updated_at: string;
  };

  type GetMyReleaseRequestsReq = {
    page?: number;
    limit?: number;
  };

  type GetMyReleaseRequestsResp = {
    list: ReleaseRequest[];
    total: number;
  };

  // 审批进度相关
  type ApprovalProgress = {
    status: string;
    steps: ApprovalProgressStep[];
  };

  type ApprovalProgressStep = {
    level: number;
    node_type: string;
    approvers: string[];
    status: string;
    details: ApprovalProgressDetail[];
  };

  type ApprovalProgressDetail = {
    approver_email: string;
    status: string;
    comment?: string;
    approved_at?: string;
  };

  // 审批实例相关
  type ApprovalInstance = {
    id: number;
    flow_id: number;
    biz_type?: string;
    biz_object_id?: string;
    status: string;
    current_level: number;
    started_at: string;
    completed_at?: string;
    created_by?: string;
  };

  // 审批任务相关
  type ApprovalTask = {
    id: number;
    instance_id: number;
    level: number;
    node_type: string;
    approvers: string[];
    status: string;
    approved_by: ApprovalTaskApprover[];
    created_at: string;
    updated_at: string;
    // 审批实例相关信息
    biz_type: string;
    biz_object_id: string;
    instance_status: string;
    instance_created_by: string;
    // 是否可以跳过（可选字段）
    optional?: boolean;
  };

  type ApprovalTaskApprover = {
    approver_email: string;
    decision: string;
    comment?: string;
    approved_at: string;
  };

  type GetMyApprovalTasksReq = {
    status?: string;
    page?: number;
    limit?: number;
  };

  type GetMyApprovalTasksResp = {
    list: ApprovalTask[];
    total: number;
  };

  type GetApprovalInstanceTasksResp = {
    list: ApprovalTask[];
  };

  type GetAllApprovalTasksReq = {
    status?: string;
    page?: number;
    limit?: number;
  };

  type GetAllApprovalTasksResp = {
    list: ApprovalTask[];
    total: number;
  };

  type SubmitApprovalReq = {
    task_id: number;
    decision: string; // approved/rejected
    comment?: string;
  };

  type SubmitApprovalResp = {
    code: number;
    message: string;
    next_step?: number;
    is_completed: boolean;
  };

  type ApproveTaskReq = {
    task_id: number;
    comment?: string;
  };

  type ApproveTaskResp = {
    success: boolean;
    errorMessage: string;
    next_step?: number;
    is_completed: boolean;
  };

  type RejectTaskReq = {
    task_id: number;
    comment?: string;
  };

  type RejectTaskResp = {
    success: boolean;
    errorMessage: string;
  };

  type SkipTaskReq = {
    task_id: number;
    comment?: string;
  };

  type SkipTaskResp = {
    success: boolean;
    errorMessage: string;
    next_step?: number;
    is_completed: boolean;
  };

  // 通用响应类型
  type BaseResponse = {
    code: number;
    message: string;
  };

  type RedisQueueMetrics = {
    region?: string;
    channel?: string;
    queueType?: string;
    queue?: string;
    queueLength?: number;
  }

  type WarehouseFulfillmentPreferenceItem = {
    key?: string;
    region?: string;
    channel?: string;
    warehouseCode?: string;
    disableFulfillment?: string;
  }

  type RuleListItem = {
    key?: number;
    disabled?: boolean;
    href?: string;
    avatar?: string;
    name?: string;
    owner?: string;
    desc?: string;
    callNo?: number;
    status?: number;
    updatedAt?: string;
    createdAt?: string;
    progress?: number;
  };

  type RuleList = {
    data?: RuleListItem[];
    /** 列表的内容总数 */
    total?: number;
    success?: boolean;
  };

  type FakeCaptcha = {
    code?: number;
    status?: string;
  };

  type LoginParams = {
    username?: string;
    password?: string;
    autoLogin?: boolean;
    type?: string;
  };

  type ErrorResponse = {
    /** 业务约定的错误码 */
    errorCode: string;
    /** 业务上的错误信息 */
    errorMessage?: string;
    /** 业务上的请求是否成功 */
    success?: boolean;
  };

  type NoticeIconList = {
    data?: NoticeIconItem[];
    /** 列表的内容总数 */
    total?: number;
    success?: boolean;
  };

  type NoticeIconItemType = 'notification' | 'message' | 'event';

  type NoticeIconItem = {
    id?: string;
    extra?: string;
    key?: string;
    read?: boolean;
    avatar?: string;
    title?: string;
    status?: string;
    datetime?: string;
    description?: string;
    type?: NoticeIconItemType;
  };

  type UserItem = {
    id?: number;
    username?: string;
    enabled?: number;
  }

  type UserUpdateRequest = {
    id?: number;
    region?: string;
    username?: string;
    enabled?: number;
  }
  type AddUserRequest = {
    region?: string;
    username?: string;
    password?: string;
  }

  type RedisQueueItem = {
    region?: string;
    queue?: string;
    "@class"?: string;
    thirdOrderId?: string;
    shopKey?: string;
    channel?: string;
    orderState?: string;
  }

  type GetRedisQueuesResp = {
    data: RedisQueueItem[];
  }

  type QueueItemDelRequest = {
    region?: string;
    channel?: string;
    thirdOrderId?: string;
  }
  type PasswordRestRequest = {
    id?: number;
    region?: string;
    username?: string;
    password?: string;
  }

  type ShopItem = {
    id?: number;
    region?: string;
    platform?: string;
    shopKey?: string;
    appKey?: string;
    appSecret?: string;
    accessToken?: string;
    refreshToken?: string;
    shopCipher?: string;
    shopName?: string;
    platformShopId?: string;
    currency?: string;
    pro?: boolean;
    multiWarehouseFlag?: number;
    status?: string;
  }

  type ShopAuthorizationItem = {
    id?: number;
    platform?: string;
    shopKey?: string;
    shopName?: string;
    platformShopId?: string;
    accessToken?: string;
    refreshToken?: string;
    shopCipher?: string;
  }
  type AppUrls = {
    [key: string]: string;
  }
  type ShopAppItem = {
    region?: string;
    currency?: string;
    platform?: string;
    appKey?: string;
    appSecret?: string;
    isSandbox?: boolean;
    installUrls?: AppUrls;
    redirectUrls?: AppUrls;
  }

  type ThirdOrderItem = {
    rawData?: string;
  }

  type ThirdStockItem = {
    sku: string;
    channel: string;
    channelProductId: string;
    quantity: number; // int64 is usually represented as number in TS
    rawData: string;
  }

  type ThirdStocksResp = {
    data: ThirdStockItem[];
  }

  type ThirdProduct = {
    id: string;
    shop_id: string;
    product_id: string;
    product_name: string;
    product_sku: string;
    product_status: string;
  }

  type ThirdProductsResp = {
    data: ThirdProduct[];
    total: number;
  }

  type TourMigration = {
    key?: string;
    region?: string;
    status?: string;
  }

  type Warehouse = {
    id?: number;
    region?: string;
    warehouseCode?: string;
    name?: string;
    thirdCode?: string;
    physicalWarehouseCode?: string;
    logicalWarehouseCode?:string
    defaultFlag?: number;
    activeFlag?: number;
    createdAt?: string;
    updatedAt?: string;
  }

  type GetWarehousesResp = {
    data?: Warehouse[];
    success?: boolean;
  }

  type GetWmsInboundOrdersReq = {
    warehouseCode: string;
    orderIds: string[];
  }

  type GetWmsOutboundOrdersReq = {
    warehouseCode: string;
    orderIds: string[];
  }

  type GetWmsOrdersResp = {
    rawData: string;
  }

  type AddShopAppsReq = {
    region: string;
    currency: string;
    platform: string;
    appKey: string;
    appSecret: string;
    isSandbox: boolean;
    environment?: string;
    installUrls?: {
      test?: string;
      prod?: string;
    };
    redirectUrls?: {
      test?: string;
      prod?: string;
    };
  }

  type AddShopAppsResp = {
    region: string;
    currency: string;
    platform: string;
    appKey: string;
    appSecret: string;
    environment?: string;
    installUrls?: {
      test?: string;
      prod?: string;
    };
    redirectUrls?: {
      test?: string;
      prod?: string;
    };
  }

  type OrderBase = {
    id: string;
    channelOrderId: string;
    state: string;
    displayState: string;
    storePlatform: string;
    shopId: number;
    storeState: string;
    warehouseId: string;
    expressId: string;
    warehouseState: string;
    orderTime: string;
    payTime: string;
    payWay: string;
    outOfStockReason: string;
    exceptionReason: string;
    preOrder: boolean;
    deliverFirst: boolean;
    remark: boolean;
    hold: boolean;
    exception: boolean;
    oversold: boolean;
    updateTime: string;
    warehouseCode: string;
    customAttributes: string;
  }

  type GetProductImageUrlsReq = {
    skus: string[];
  }

  type ProductImageUrl = {
    barcode?: string;
    sku: string;
    image_1?: string;
    image_2?: string;
    image_3?: string;
    image_4?: string;
    image_5?: string;
    image_6?: string;
  }

  type GetProductImageUrlsResp = {
    data: ProductImageUrl[];
  }

  type FixSkusImagesReq = {
    region: string;
    skus: string[];
  }

  type FixSkusImagesResp = {
    success: boolean;
    error?: string;
  }

  type PushOrderToScmWithoutCancelReq = {
    region: string;
    orderId: string;
    authorization: string;
  }

  type PushOrderToScmWithoutCancelResp = {
    region: string;
    orderId: string;
    success: boolean;
    error?: string;
  }

  type ScmRequestAuditLog = {
    id: number;
    requestUrl: string;
    requestMethod: string;
    requestPayload: string;
    createdAt: string;
    convertedPayload: string;
    responsePayload: string;
  }

  type GetScmRequestAuditLogsResp = {
    data: ScmRequestAuditLog[];
  }

  type InitSearchTermsConfigReq = {
    region: string;
    warehouseTermId: string;
    warehouseName: string;
  }

  type InitSearchTermsConfigResp = {
    success: boolean;
    error?: string;
  }

  type OrderSyncMetrics = {
    totalOrders: OrderBase[];
    ordersWithoutTracking: OrderBase[];
    ordersWithoutDelivery: OrderBase[];
    ordersWithStockIssue: OrderBase[];
    ordersWithCombineSkuIssue: OrderBase[];
    wmsOrderTotal: string[];
    isSynchronized: boolean;
    missingOrders: OrderBase[];
    wmsDocuments: OrderBase[];
  }

  type OncallSchedule = {
    dayOfWeek: number;
    primary: string;
    secondary: string;
  }

  type OncallConfig = {
    schedules: OncallSchedule[];
  }

  type GetOncallConfigResp = {
    data: OncallConfig;
  }

  type UpdateOncallConfigReq = {
    schedules: OncallSchedule[];
  }

  type UpdateOncallConfigResp = {
    data: OncallConfig;
  }

  type GetCurrentOncallResp = {
    primary: string;
    secondary: string;
    dayOfWeek: number;
  }

  type GetFinancialSalesEntityResp = {
    rawData: string;
  }

  type InitShopProductResp = {
    success: boolean;
    error?: string;
  }

  type SiteConfig = {
    region: string;
    regionName: string;
    currency: string;
    warehouse: string;
    warehouseName: string;
    provinceId: string;
    cityId: string;
    detailId: string;
  }

  type GetSiteConfigResp = {
    data: SiteConfig[];
  }

  type UpdateSiteConfigReq = {
    region: string;
    regionName: string;
    currency: string;
    warehouse: string;
    warehouseName: string;
  }

  type UpdateSiteConfigResp = {
    data: SiteConfig;
  }

  type DeleteSiteConfigResp = {
    success: boolean;
    error?: string;
  }

  type QueueConsumptionStatus = {
    region: string;
    platform: string;
    enabled: string;
    redisKey: string;
  }

  type GetQueueConsumptionStatusResp = {
    data: QueueConsumptionStatus[];
  }

  type UpdateQueueConsumptionStatusReq = {
    region: string;
    platform: string;
    enabled: string;
  }

  type UpdateQueueConsumptionStatusResp = {
    success: boolean;
    error?: string;
  }

  type SyncOrderWarehouseStateReq = {
    region: string;
    orderIds: string[];
  }

  type SyncOrderWarehouseStateResp = {
    success: boolean;
    error?: string;
  }

  type ReturnMerchandiseBackWarehouseReq = {
    region: string;
    warehouseInNo: string;
  }

  type ReturnMerchandiseBackWarehouseResp = {
    success?: boolean;
    errorMessage?: string;
  }

  type UpdateDeliverItemSalesTransactionReq = {
    region: string;
    relOrderNo: string;
    sku: string;
    subjectCode: string;
    changeQuantity: number;
    createTime: string;
    transactionLinkCode: string;
  }

  type UpdateDeliverItemSalesTransactionResp = {
    success: boolean;
    message: string;
  }

  type SyncShopOrderWithChannelReq = {
    region: string;
    shopId: string;
    orderId: string;
  }

  type SyncShopOrderWithChannelResp = {
    success: boolean;
    error?: string;
  }

// Translation related types
  type Translation = {
    id?: number;
    key?: string;
    namespace?: string;
    default_text?: string;
    created_at?: string;
    values?: TranslationValue[];
  }

  type TranslationValue = {
    id?: number;
    translation_id?: number;
    language_code?: string;
    translated_text?: string;
    status?: string;
    updated_at?: string;
    translation?: Translation;
  }

  type GetTranslationsRequest = {
    namespace?: string;
    key?: string;
    search?: string;
    status?: string;
    page?: number;
    limit?: number;
  }

  type GetTranslationsResponse = {
    data?: Translation[];
    total?: number;
  }

  type CreateTranslationRequest = {
    key?: string;
    namespace?: string;
    default_text?: string;
  }

  type UpdateTranslationRequest = {
    key?: string;
    namespace?: string;
    default_text?: string;
  }

  type GetTranslationValuesRequest = {
    language_code?: string;
    status?: string;
    namespace?: string;
    page?: number;
    limit?: number;
  }

  type GetTranslationValuesResponse = {
    data?: TranslationValue[];
    total?: number;
  }

  type CreateTranslationValueRequest= {
    translation_id?: number;
    language_code?: string;
    translated_text?: string;
    status?: string;
  }

  type UpdateTranslationValueRequest= {
    translated_text?: string;
    status?: string;
  }

  type ExportTranslationsRequest ={
    language_code: string;
    namespace?: string;
    device?: 'web' | 'android' | 'ios';
  }

  type ExportTranslationsResponse ={
    data?: string;
  }

  type AITranslationRequest ={
    id: number;
    language_code: string;
    previous_translations?: string[];
  }

  type AITranslationResponse ={
    data: string;
  }

  type GenerateTranslationKeyRequest ={
    default_text: string;
    module: string;
    context?: string;
  }

  type GenerateTranslationKeyResponse ={
    key: string;
  }

  type CheckTranslationKeyRequest ={
    key: string;
    namespace: string;
  }

  type CheckTranslationKeyResponse ={
    exists: boolean;
    translation?: Translation;
  }

  type ErrorCode = {
    name: string;
    code: number;
    message: string;
  }

  type ImportFromErrorCodesRequest = {
    error_codes: ErrorCode[];
  }

  type ImportFromErrorCodesResponse = {
    success: number;
    errorMessage: string;
  }


// Influencer Video related types
  type InfluencerVideo = {
    id?: number;
    category?: string;
    person_in_charge?: string;
    contact_date?: string;
    platform?: string;
    influencer_username?: string;
    influencer_contact?: string;
    influencer_followers?: number;
    store_name?: string;
    product_name?: string;
    sku?: string;
    status?: string;
    note?: string;
    rate?: number;
    video_link?: string;
    video_uploaded_date?: string;
    video_views?: number;
    video_likes?: number;
    video_comments?: number;
    video_shares?: number;
    video_ads_code?: string;
    remark?: string;
    gmv?: string;
    created_at?: string;
    created_by?: string;
    updated_at?: string;
    updated_by?: string;
    last_refresh_at?: string;
  }


  type CreateInfluencerVideoResp= {
    success: boolean;
    errorMessage?: string;
    data: InfluencerVideo;
  }

  type UpdateInfluencerVideoRequest = {
    id: number;
    InfluencerVideo
  }

  type GetInfluencerVideosReq= {
    id?: number;
    current?: number;
    page_size?: number;
    status?: string;
    platform?: string;
    category?: string;
    person_in_charge?: string;
    influencer_username?: string;
    store_name?: string;
    sku?: string;
    video_link?: string;
    sort_field?: string;
    sort_order?: string;
  }

  type GetInfluencerVideosResp= {
    data: InfluencerVideo[];
    total?: number;
  }

// Influencer GMV Summary related types
  type InfluencerGmvSummary ={
    id: number;
    influencer_username: string;
    total_gmv: number;
    date: string;
    created_at?: string;
    created_by?: string;
    updated_at?: string;
    updated_by?: string;
  }

  type CreateInfluencerGmvSummaryReq ={
    influencer_username: string;
    total_gmv: number;
    date: string;
  }

  type CreateInfluencerGmvSummaryResp= {
    id: number;
  }

  type UpdateInfluencerGmvSummaryReq ={
    id: number;
    total_gmv: number;
    date: string;
  }

  type GetInfluencerGmvSummaryReq= {
    influencer_username?: string;
    sort_field?: string;
    sort_order?: string;
    current?: number;
    page_size?: number;
  }

  type GetInfluencerGmvSummaryResp= {
    data: InfluencerGmvSummary[];
    total?: number;
  }

// Influencer Video Daily Metrics related types
  type InfluencerVideoDailyMetrics= {
    id: number;
    video_id: number;
    record_date: string;
    influencer_followers: number;
    video_views: number;
    video_likes: number;
    video_comments: number;
    video_shares: number;
    created_at: string;
    updated_at: string;
  }

  type GetInfluencerVideoDailyMetricsReq= {
    video_id?: number;
    start_date?: string;
    end_date?: string;
    sort_field?: string;
    sort_order?: string;
    current?: number;
    page_size?: number;
  }

  type GetInfluencerVideoDailyMetricsResp= {
    data: InfluencerVideoDailyMetrics[];
    total: number;
  }

// Influencer Video Datasource related types
  type InfluencerVideoDatasource ={
    id: number;
    category: string;
    sheet_token: string;
    sheet_id: string;
    created_at: string;
    created_by: string;
    updated_at: string;
    updated_by: string;
  }

  type CreateInfluencerVideoDatasourceReq ={
    category: string;
    sheet_token: string;
    sheet_id: string;
  }

  type CreateInfluencerVideoDatasourceResp ={
    id: number;
  }

  type GetInfluencerVideoDatasourceReq ={
    id: number;
  }

  type GetInfluencerVideoDatasourceResp ={
    influencer_video_datasource: InfluencerVideoDatasource;
  }

  type GetInfluencerVideoDatasourcesReq ={
    category?: string;
  }

  type GetInfluencerVideoDatasourcesResp ={
    data: InfluencerVideoDatasource[];
  }

  type UpdateInfluencerVideoDatasourceReq ={
    id: number;
    category: string;
    sheet_token: string;
    sheet_id: string;
  }

  type DeleteInfluencerVideoDatasourceReq ={
    id: number;
  }

// Influencer Product Reference related types
  type InfluencerProductReference = {
    sku: string;
    product_name: string;
    updated_at: string;
    created_at: string;
  }

  type CreateInfluencerProductReferenceReq = {
    sku: string;
    product_name: string;
  }

  type CreateInfluencerProductReferenceResp = {
    sku: string;
  }

  type GetInfluencerProductReferenceReq = {
    sku: string;
  }

  type GetInfluencerProductReferenceResp = {
    InfluencerProductReference
  }

  type GetInfluencerProductReferencesReq = {
    sku?: string;
    product_name?: string;
    page?: number;
    page_size?: number;
  }

  type GetInfluencerProductReferencesResp = {
    data: InfluencerProductReference[];
    total: number;
  }

  type UpdateInfluencerProductReferenceReq = {
    sku: string;
    product_name: string;
  }

  type DeleteInfluencerProductReferenceReq = {
    sku: string;
  }

  type BatchCreateInfluencerProductReferenceReq = {
    references: CreateInfluencerProductReferenceReq[];
  }

  type BatchCreateInfluencerProductReferenceResp = {
    success: number;
    failed: number;
  }

// Live Performance related types
  type LivePerformance ={
    id: number;
    hash: string;
    shop: string;
    live_room: string;
    shift: string;
    session: number;
    host: string;
    assistant: string;
    livestream_title: string;
    start_time: string;
    start_date: string;
    start_month: string;
    start_week: string;
    duration: number;
    duration_hours: number;
    direct_gmv: number;
    direct_gmv_rmb: number;
    gross_revenue: number;
    items_sold: number;
    buyers: number;
    orders_paid_for: number;
    show_gpm: number;
    watch_gpm: number;
    views: number;
    viewers: number;
    pcu: number;
    new_followers: number;
    avd: number;
    likes: number;
    comments: number;
    shares: number;
    product_impressions: number;
    product_clicks: number;
    interaction_rate: number;
    created_at: string;
    updated_at: string;
  }

  type GetLivePerformancesReq ={
    id?: number;
    current?: number;
    page_size?: number;
    shop?: string;
    live_room?: string;
    host?: string;
    shift?: string;
    sort_field?: string;
    sort_order?: string;
    start_date?: string;
    end_date?: string;
    start_month?: string;
  }

  type GetLivePerformancesResp ={
    data: LivePerformance[];
    total: number;
  }

  type CreateLivePerformanceReq ={
    shop: string;
    live_room: string;
    shift: string;
    session: number;
    host: string;
    assistant: string;
    livestream_title: string;
    start_time: string;
    duration: number;
    direct_gmv: number;
    direct_gmv_rmb: number;
    gross_revenue: number;
    items_sold: number;
    buyers: number;
    orders_paid_for: number;
    show_gpm: number;
    watch_gpm: number;
    views: number;
    viewers: number;
    pcu: number;
    new_followers: number;
    avd: number;
    likes: number;
    comments: number;
    shares: number;
    product_impressions: number;
    product_clicks: number;
    interaction_rate: number;
  }

  type CreateLivePerformanceResp ={
    id: number;
  }

  type UpdateLivePerformanceReq = {
    id: number;
    CreateLivePerformanceReq,
  }

  type DeleteLivePerformanceReq ={
    id: number;
  }

// Live Performance Datasource related types
  type LivePerformanceDatasource= {
    id: number;
    category: string;
    sheet_token: string;
    sheet_id: string;
    created_at: string;
    created_by: string;
    updated_at: string;
    updated_by: string;
  }

  type CreateLivePerformanceDatasourceReq ={
    category: string;
    sheet_token: string;
    sheet_id: string;
  }

  type CreateLivePerformanceDatasourceResp= {
    id: number;
  }

  type GetLivePerformanceDatasourceReq ={
    id: number;
  }

  type GetLivePerformanceDatasourceResp= {
    live_performance_datasource: LivePerformanceDatasource;
  }

  type GetLivePerformanceDatasourcesReq= {
    category?: string;
  }

  type GetLivePerformanceDatasourcesResp= {
    data: LivePerformanceDatasource[];
  }

  type UpdateLivePerformanceDatasourceReq ={
    id: number;
    category: string;
    sheet_token: string;
    sheet_id: string;
  }

  type DeleteLivePerformanceDatasourceReq ={
    id: number;
  }

// Live Performance Goal related types
  type LivePerformanceGoal ={
    month: string;
    goal: number;
  }

  type CreateLivePerformanceGoalReq ={
    month: string;
    goal: number;
  }

  type CreateLivePerformanceGoalResp= {
    month: string;
  }

  type GetLivePerformanceGoalReq= {
    month: string;
  }

  type GetLivePerformanceGoalResp= {
    live_performance_goal: LivePerformanceGoal;
  }

  type GetLivePerformanceGoalsReq= {
    month?: string;
  }

  type GetLivePerformanceGoalsResp= {
    data: LivePerformanceGoal[];
  }

  type UpdateLivePerformanceGoalReq ={
    month: string;
    goal: number;
  }

  type DeleteLivePerformanceGoalReq ={
    month: string;
  }


  type BaseResponse= {
    code: number;
    msg: string;
  }

// 数据库相关类型
  type Database ={
    id: number;
    instance_name: string;
    database_name: string;
    created_at: string;
    updated_at: string;
  }

  type GetDatabasesResp= {
    data: Database[];
  }

  type CreateDatabaseReq ={
    instance_name: string;
    database_name: string;
  }

  type UpdateDatabaseReq ={
    instance_name: string;
    database_name: string;
  }

// GitLab 相关类型
  type Repository ={
    id: number;
    project_id: number;
    name: string;
    url: string;
    description: string;
    is_active: boolean;
    created_at: string;
    updated_at: string;
  }

  type GetRepositoriesResp ={
    data: Repository[];
  }

  type ReleaseBranch ={
    id: number;
    release_id: number;
    repository_id: number;
    branch_name: string;
    status: string;
    created_at: string;
    updated_at: string;
  }

  type AddReleaseBranchReq ={
    repository_id: number;
    branch_name: string;
  }

// Application 相关类型
  type Application = {
    id: number;
    name: string;
    description: string;
    gitlab_project_id: number;
    gitlab_repo_name: string;
    gitlab_repo_url: string;
    *********************: string;
    config_file_path: string;
    configmap: string;
    configmap_key: string;
    gitlab_repo_branches?: GitLabBranch[];
    is_active: boolean;
    created_at: string;
    updated_at: string;
  }

  type GitLabBranch = {
    name: string;
    commit: string;
  }

  type ApplicationBranch = {
    application_id: number;
    branch_name: string;
  }

  // 配置文件对比相关类型
  type GetApplicationConfigFileContentReq = {
    id: number;
    branch: string;
  }

  type GetApplicationConfigFileContentResp = {
    content: string;
  }

  type GetApplicationConfigMapContentReq = {
    id: number;
  }

  type GetApplicationConfigMapContentResp = {
    content: string;
  }

  type AIConfigCompareReq = {
    git_content: string;
    configmap_content: string;
    application_name: string;
  }

  type AIConfigCompareResp = {
    differences: string[];
    summary: string;
    suggestions: string[];
  }

// Jenkins 相关类型
  type JenkinsJob= {
    name?: string;
    url: string;
    class: string;
    jobFullPath: string;
  }

  type GetJenkinsJobsReq ={
    name?: string;
  }

  type GetJenkinsJobsResp= {
    data: JenkinsJob[];
  }

  type ReleaseJenkinsJob= {
    id: number;
    release_id: number;
    job_id: number;
    status: string;
    created_at: string;
    updated_at: string;
  }

  type AddReleaseJenkinsJobReq ={
    job_id: number;
  }

// ConfigMap related types
  type GetConfigMapsReq ={
    namespace?: string;
  }

  type GetConfigMapsResp ={
    data: ConfigMapInfo[];
  }

  type ConfigMapInfo= {
    name: string;
    contents: ConfigMapContent[];
  }

  type ConfigMapContent ={
    key: string;
    content: string;
  }

  type GetConfigMapContentReq= {
    namespace: string;
    name: string;
    key: string;
  }

  type GetConfigMapContentResp ={
    content: string;
  }

// 审核相关类型
  type Reviewer ={
    id: number;
    release_id: number;
    reviewer_id: number;
    is_required: boolean;
    status: string;
    comment: string;
    reviewed_at: string;
    created_at: string;
    updated_at: string;
  }

  type AddReviewerReq ={
    reviewer_id: number;
    is_required: boolean;
  }

  type ReviewReq= {
    status: string;
    comment: string;
  }

// 通知相关类型
  type Notification ={
    id: number;
    release_id: number;
    type: string;
    content: string;
    status: string;
    created_at: string;
    updated_at: string;
  }

  type GetNotificationsReq ={
    release_id?: number;
  }

  type GetNotificationsResp= {
    data: Notification[];
  }

// Kubernetes 相关类型
  type KubernetesNamespace ={
    id: number;
    name: string;
    cluster_name: string;
    created_at: string;
    updated_at: string;
  }

  type GetNamespacesReq ={
    name?: string;
  }

  type GetNamespacesResp ={
    data: KubernetesNamespace[];
  }

  type CreateNamespaceReq ={
    name: string;
    cluster_name: string;
  }

  type UpdateNamespaceReq ={
    name: string;
    cluster_name: string;
  }

// GitLab branch related types

  type GetGitLabBranchesReq ={
    repository_id: number;
  }

  type GetGitLabBranchesResp ={
    data: GitLabBranch[];
  }

  type RoleInfo = {
    name: string;
    description: string;
    systems: Record<string, string[]>;
  }

  type GetRolesResp = {
    data: RoleInfo[];
  }

  type CreateRoleReq = {
    name: string;
    description: string;
    systems: Record<string, string[]>;
  }

  type CreateRoleResp = {
    success: boolean;
    data: RoleInfo;
    message: string;
  }

  type UpdateRoleReq = {
    name: string;
    description?: string;
    systems?: Record<string, string[]>;
  }

  type UpdateRoleResp = {
    success: boolean;
    data: RoleInfo;
    message: string;
  }

  type DeleteRoleResp = {
    success: boolean;
    message: string;
  }

  type PathInfo = {
    path: string;
    system: string;
    method: string;
    description: string;
  }

  type GetAvailablePathsResp = {
    data: PathInfo[];
  }

  // DMS related types
  type DMSQueryReq = {
    system: string;
    region: string;
    sql: string;
  }

  type DMSQueryResult = {
    columns: string[];
    rows: string[][];
    error?: string;
  }

  type ExecuteDMSMutationReq = {
    system: string;
    region: string;
    sql: string;
  }

  type ExecuteDMSMutationResp = {
    success: boolean;
    message?: string;
    affected_rows?: number;
    execution_time?: string;
    sql_statement?: string;
  }

  // Release Plans related types
  type ReleasePlan = {
    id: string;
    name: string;
    description: string;
    approval_flow_id: number;
    approval_instance_id: number;
    merge_request_urls: string[];
    prd_urls: string[];
    tech_design_urls: string[];
    release_manual_urls: string[];
    status: string;
    created_by?: string;
    created_at: string;
    updated_at: string;
    application_tasks?: ReleaseApplicationTask[];
    db_migration_tasks?: ReleaseDbMigrationTask[];
  }

  type CreateReleasePlanReq = {
    name: string;
    description: string;
    approval_flow_id: number;
    merge_request_urls: string[];
    prd_urls: string[];
    tech_design_urls: string[];
    release_manual_urls: string[];
    application_tasks: CreateApplicationTaskReq[];
    db_migration_tasks: CreateDbMigrationTaskReq[];
  }

  type CreateApplicationTaskReq = {
    application_id: number;
    git_branch: string;
    config_data?: string;
  }

  type CreateDbMigrationTaskReq = {
    sql_script: string;
    site: string;
    region: string;
  }

  type ReleaseApplicationTask = {
    id: string;
    plan_id: string;
    application_id: number;
    git_branch: string;
    config_data?: string;
    jenkins_queue_item_id: string;
    jenkins_build_number?: number;
    jenkins_build_status?: string;
    status: string;
    executed: boolean;
    executed_at: string;
    created_at: string;
    updated_at: string;
  }

  type ReleaseDbMigrationTask = {
    id: string;
    plan_id: string;
    sql_script: string;
    site: string;
    region: string;
    status: string;
    executed: boolean;
    executed_at: string;
    created_at: string;
    updated_at: string;
  }

  type GetReleasePlansReq = {
    name?: string;
    status?: string;
  }

  type GetReleasePlansResp = {
    data: ReleasePlan[];
  }

  type UpdatePlanStatusReq = {
    plan_id: string;
    status: string;
  }

  type ExecuteTaskResp = {
    task_id: string;
    jenkins_queue_item_id?: string;
    status: string;
    message: string;
  }

  type ExecuteAllTasksResp = {
    plan_id: string;
    executed_task_ids: string[];
    failed_task_ids: string[];
    message: string;
  }

  type XxlExecutor = {
    id: number;
    appName: string;
    title: string;
    addressType: number;
    addressList: string;
    updateTime: string;
    registryList: string[];
  }

  type ListXxlExecutorsResp = {
    data: XxlExecutor[];
  }

  type XxlJob = {
    id: number;
    jobGroup: number;
    jobDesc: string;
    addTime: string;
    updateTime: string;
    author: string;
    executorHandler: string;
    scheduleType: 'CRON' | 'FIX_RATE';
    scheduleConf: string;
    appName: string;
  }

  type CopyJobsFromIndonesiaReq = {
    id: number;
    appName: string;
  }

  type CopyJobsFromIndonesiaResp = {
    success: boolean;
    message?: string;
    data: XxlJob[];
  }

  type GetPresignedUrlResp = {
    region: string;
    bucket: string;
    expiration: string;
    keyPrefix: string;
    accessKeyId: string;
    accessKeySecret: string;
    securityToken: string;
  }

  type OhsomeApkVersion = {
    version: string;
    fileName: string;
    downloadUrl: string;
    uploadTime: string;
    fileSize: number;
  }

  type GetOhsomeApkVersionsResp = {
    data: OhsomeApkVersion[];
  }

  // O2O Grab Config 相关类型
  type O2OGrabConfig = {
    region: string;
    accessKeyId: string;
    accessKeySecret: string;
    tokenPoint: string;
    apiPointPrefix: string;
  }

  type GetO2OGrabConfigListResp = {
    data: O2OGrabConfig[];
  }

  type AddO2OGrabConfigReq = {
    region: string;
    accessKeyId: string;
    accessKeySecret: string;
  }

  type AddO2OGrabConfigResp = {
    success: boolean;
    message: string;
  }

  type UpdateO2OGrabConfigReq = {
    region: string;
    accessKeyId: string;
    accessKeySecret: string;
  }

  type UpdateO2OGrabConfigResp = {
    success: boolean;
    message: string;
  }

  type DeleteO2OGrabConfigReq = {
    region: string;
  }

  type DeleteO2OGrabConfigResp = {
    success: boolean;
    message: string;
  }

  // 图片去Logo相关类型
  type RemoveLogoFromImageReq = {
    imageUrl: string;
  }

  type RemoveLogoFromImageResp = {
    success: boolean;
    message?: string;
    imageData?: string;   // base64编码的图片数据
    contentType?: string; // 图片类型
  }

  // Funifun奖励商品相关类型
  type FunifunRewardItem = {
    id?: number;                       // 商品ID
    name: string;                      // 商品名称
    display_order: number;             // 展示顺序
    required_tickets?: number;         // 所需彩票数
    required_stars?: number;           // 所需星星数
    status: string;                    // 状态：active-上架, inactive-下架
    description?: string;              // 商品描述
    main_image_url?: string;           // 主图
    sku: string;                       // SKU编码
    created_at?: string;               // 创建时间
    updated_at?: string;               // 更新时间
    created_by?: number;               // 创建人
    created_by_name?: string;          // 创建人姓名
    updated_by?: number;               // 更新人
    updated_by_name?: string;          // 更新人姓名
    deleted_by?: number;               // 删除人
    deleted_by_name?: string;          // 删除人姓名
    deleted_at?: string;               // 删除时间
    flag_deleted?: boolean;            // 是否删除：0-未删除，1-已删除
  }

  type ListFunifunRewardItemsResp = {
    total: number;                     // 总数
    list: FunifunRewardItem[];         // 列表
  }

  type SyncFunifunRewardItemsResp = {
    success: boolean;
    errorMessage?: string;
  }

  // Feature Control types
  type FeatureConfig = {
    enabled: boolean;
    [key: string]: any;
  }

  type NamespaceConfig = {
    description: string;
    enabled: boolean;
    features: Record<string, FeatureConfig>;
  }

  type FeatureToggle = {
    enabled: boolean;
    namespaces: Record<string, NamespaceConfig>;
  }

  type FeatureControlConfig = {
    feature_toggle: FeatureToggle;
  }

  type GetFeatureControlConfigResp = {
    success: boolean;
    errorMessage?: string;
    data: FeatureControlConfig;
  }

  type UpdateFeatureControlConfigReq = {
    feature_toggle: FeatureToggle;
  }

  type UpdateFeatureControlConfigResp = {
    success: boolean;
    errorMessage?: string;
  }
}
