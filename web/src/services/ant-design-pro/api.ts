// @ts-ignore
/* eslint-disable */
import { request } from '@umijs/max';

/** 获取当前的用户 GET /api/currentUser */
export async function currentUser(options?: { [key: string]: any }) {
  return request<API.CurrentUser>('/api/user/info', {
    method: 'GET',
    ...(options || {}),
  });
}

/** 退出登录接口 POST /api/login/outLogin */
export async function outLogin(options?: { [key: string]: any }) {
  return request<Record<string, any>>('/api/login/outLogin', {
    method: 'POST',
    ...(options || {}),
  });
}

/** 登录接口 POST /api/login/account */
export async function login(body: API.LoginParams, options?: { [key: string]: any }) {
  return request<API.LoginResult>('/auth/login', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 获取 users 列表 GET /api/users */
export async function users(
  params: {
    // query
    /** 当前的页码 */
    current?: number;
    /** 页面的容量 */
    pageSize?: number;
  },
  options?: { [key: string]: any },
) {
  return request<API.UserItem>('/api/users', {
    method: 'GET',
    params: {
      ...params,
    },
    ...(options || {}),
  });
}

export async function shops(
  params: {
    // query
    /** 当前的页码 */
    current?: number;
    /** 页面的容量 */
    pageSize?: number;
  },
  options?: { [key: string]: any },
) {
  return request<API.ShopItem>('/api/shops', {
    method: 'GET',
    params: {
      ...params,
    },
    ...(options || {}),
  });
}

// get shop-authorizations
export async function shopAuthorizations(
  params: {
    // query
    /** 当前的页码 */
    current?: number;
    /** 页面的容量 */
    pageSize?: number;
  },
  options?: { [key: string]: any },
) {
  return request<API.ShopAuthorizationItem>('/api/shop-authorizations', {
    method: 'GET',
    params: {
      ...params,
    },
    ...(options || {}),
  });
}

// get shop-apps
export async function shopApps(
  params: {
    // query
    /** 当前的页码 */
    current?: number;
    /** 页面的容量 */
    pageSize?: number;
  },
  options?: { [key: string]: any },
) {
  return request<API.ShopAppItem>('/api/shop-apps', {
    method: 'GET',
    params: {
      ...params,
    },
    ...(options || {}),
  });
}

/** 添加 shop app */
export async function addShopApps(body: API.AddShopAppsReq, options?: { [key: string]: any }) {
  return request<API.AddShopAppsResp>('/api/shop-apps', {
    method: 'POST',
    data: body,
    ...(options || {}),
  });
}

/** GET /api/maintenance/redis-queues */
export async function redisQueues(
  params: {
    // query
    /** 当前的页码 */
    current?: number;
    /** 页面的容量 */
    pageSize?: number;
  },
  options?: { [key: string]: any },
) {
  return request<API.GetRedisQueuesResp>('/api/maintenance/redis-queues', {
    method: 'GET',
    params: {
      ...params,
    },
    ...(options || {}),
  });
}

/** 获取preference列表 GET /api/fulfillment/preference */
export async function fulfillmentPreference(
  params: {
    // query
    /** 当前的页码 */
    current?: number;
    /** 页面的容量 */
    pageSize?: number;
  },
  options?: { [key: string]: any },
) {
  return request<API.WarehouseFulfillmentPreferenceItem>('/api/fulfillment/preferences', {
    method: 'GET',
    params: {
      ...params,
    },
    ...(options || {}),
  });
}

/** 更新 fulfillment Preference */
export async function updateFulfillmentPreference(options?: { [key: string]: any }) {
  return request<API.WarehouseFulfillmentPreferenceItem>('/api/fulfillment/preferences', {
    method: 'PUT',
    data:{
      ...(options || {}),
    }
  });
}

/** GET /api/metrics/redis-queue */
export async function getRedisQueueMetrics(
  params?: {
    region?: string;
  },
  options?: { [key: string]: any }
) {
  return request<API.GetRedisQueueMetricsResp>('/api/metrics/redis-queue', {
    method: 'GET',
    params: {
      ...params,
    },
    ...(options || {}),
  });
}

/** GET /api/metrics/orders */
export async function getOrderMetrics(
  params?: {
    region?: string;
  },
  options?: { [key: string]: any }
) {
  return request<API.GetOrderMetricsResp>('/api/metrics/orders', {
    method: 'GET',
    params: {
      ...params,
    },
    ...(options || {}),
  });
}

/** enable user */
export async function enableUser(body: API.UserUpdateRequest, options?: { [key: string]: any }) {
  return request<API.UserItem>(`/api/users/${body.id}/enable`, {
    method: 'POST',
    data: body,
    ...(options || {}),
  });
}

/** disable user */
export async function disableUser(body: API.UserUpdateRequest, options?: { [key: string]: any }) {
  return request<API.UserItem>(`/api/users/${body.id}/disable`, {
    method: 'POST',
    data: body,
    ...(options || {}),
  });
}

/** reset user password */
export async function resetPassword(body: API.PasswordRestRequest, options?: { [key: string]: any }) {
  return request<API.UserItem>(`/api/users/${body.id}/password`, {
    method: 'POST',
    data: body,
    ...(options || {}),
  });
}

/** add user */
export async function addUser(body: API.AddUserRequest, options?: { [key: string]: any }) {
  return request<API.UserItem>(`/api/users`, {
    method: 'POST',
    data: body,
    ...(options || {}),
  });
}

export async function addShop(body: API.ShopItem, options?: { [key: string]: any }) {
  return request<API.ShopItem>(`/api/shops`, {
    method: 'POST',
    data: body,
    ...(options || {}),
  });
}

// sync shop POST shops/:id/sync
export async function syncShop(body: API.ShopItem, options?: { [key: string]: any }) {
  return request<API.ShopItem>(`/api/shops/${body.id}/sync`, {
    method: 'POST',
    data: body,
    ...(options || {}),
  });
}

export async function updateShop(body: API.ShopItem, options?: { [key: string]: any }) {
  return request<API.ShopItem>(`/api/shops/${body.id}`, {
    method: 'PUT',
    data: body,
    ...(options || {}),
  });
}

export async function patchShopStatus(
  params: { region: string; id: number },
  body: { status: string },
  options?: { [key: string]: any }
) {
  return request<{ success: boolean; errorMessage?: string }>(`/api/regions/${params.region}/shops/${params.id}/status`, {
    method: 'PATCH',
    data: body,
    ...(options || {}),
  });
}

// add shop authorization
export async function addShopAuthorization(body: API.ShopAuthorizationItem, options?: { [key: string]: any }) {
  return request<API.ShopAuthorizationItem>(`/api/shop-authorizations`, {
    method: 'POST',
    data: body,
    ...(options || {}),
  });
}

/** delete queue item */
export async function deleteQueueItem(body: API.QueueItemDelRequest, options?: { [key: string]: any }) {
  return request<API.RedisQueueItem>(`/api/maintenance/redis-queues/${body.channel}/item`, {
    method: 'DELETE',
    data: body,
    ...(options || {}),
  });
}

export async function getThirdOrder(params: {
  id: string
  region: string
  platform: string
  shopKey: string
}, options?: { [key: string]: any }) {
  return request<API.ThirdOrderItem>(`/api/${params.platform}-orders/${params.id}`, {
    method: 'GET',
    params: {
      ...params,
    },
    ...(options || {}),
  });
}

/** 查询渠道库存 GET /api/{platform}-stocks */
export async function getThirdStocks(params: {
  region: string;
  platform: string;
  shopKey: string;
  skus: string;
}, options?: { [key: string]: any }) {
  return request<API.ThirdStocksResp>(`/api/${params.platform}-stocks`, {
    method: 'GET',
    params: {
      ...params,
    },
    ...(options || {}),
  });
}

/** 获取TikTok产品列表 GET /api/region/:region/tiktok/:shop_id/products */
export async function getTikTokProducts(params: {
  region: string;
  shop_id: number;
  page?: number;
  pageSize?: number;
}, options?: { [key: string]: any }) {
  return request<API.ThirdProductsResp>(`/api/region/${params.region}/tiktok/${params.shop_id}/products`, {
    method: 'GET',
    params: {
      page: params.page,
      pageSize: params.pageSize,
    },
    ...(options || {}),
  });
}

/** 获取Shopee产品列表 GET /api/region/:region/shopee/:shop_id/products */
export async function getShopeeProducts(params: {
  region: string;
  shop_id: number;
  page?: number;
  pageSize?: number;
}, options?: { [key: string]: any }) {
  return request<API.ThirdProductsResp>(`/api/region/${params.region}/shopee/${params.shop_id}/products`, {
    method: 'GET',
    params: {
      page: params.page,
      pageSize: params.pageSize,
    },
    ...(options || {}),
  });
}

/** 获取Lazada产品列表 GET /api/region/:region/lazada/:shop_id/products */
export async function getLazadaProducts(params: {
  region: string;
  shop_id: number;
  page?: number;
  pageSize?: number;
}, options?: { [key: string]: any }) {
  return request<API.ThirdProductsResp>(`/api/region/${params.region}/lazada/${params.shop_id}/products`, {
    method: 'GET',
    params: {
      page: params.page,
      pageSize: params.pageSize,
    },
    ...(options || {}),
  });
}

/** 更新 tour migrations */
export async function updateTourMigrations(options?: { [key: string]: any }) {
  return request<API.TourMigration>(`/api/tour-migrations/${options?.region}`, {
    method: 'PUT',
    data:{
      ...(options || {}),
    }
  });
}

/** 获取 tour migrations */
export async function tourMigrations(
  params: {
    // query
    /** 当前的页码 */
    current?: number;
    /** 页面的容量 */
    pageSize?: number;
  },
  options?: { [key: string]: any },
) {
  return request<API.TourMigration>('/api/tour-migrations', {
    method: 'GET',
    params: {
      ...params,
    },
    ...(options || {}),
  });
}

/** 获取仓库列表 GET /api/warehouses */
export async function warehouses(
  params: {
    warehouseCode?: string;
    region?: string;
  },
  options?: { [key: string]: any },
) {
  return request<API.GetWarehousesResp>('/api/warehouses', {
    method: 'GET',
    params: {
      ...params,
    },
    ...(options || {}),
  });
}

/** 新增仓库 POST /api/warehouses */
export async function addWarehouse(body: API.Warehouse, options?: { [key: string]: any }) {
  return request<API.Warehouse>('/api/warehouses', {
    method: 'POST',
    data: body,
    ...(options || {}),
  });
}

/** 更新仓库 PUT /api/warehouses/:id */
export async function updateWarehouse(body: API.Warehouse, options?: { [key: string]: any }) {
  return request<API.Warehouse>(`/api/warehouses/${body.id}`, {
    method: 'PUT',
    data: body,
    ...(options || {}),
  });
}

/** 查询入库单 POST /api/wms/inbound-orders */
export async function getWmsInboundOrders(body: API.GetWmsInboundOrdersReq, options?: { [key: string]: any }) {
  return request<API.GetWmsOrdersResp>('/api/wms/inbound-orders', {
    method: 'POST',
    data: body,
    ...(options || {}),
  });
}

/** 查询出库单 POST /api/wms/outbound-orders */
export async function getWmsOutboundOrders(body: API.GetWmsOutboundOrdersReq, options?: { [key: string]: any }) {
  return request<API.GetWmsOrdersResp>('/api/wms/outbound-orders', {
    method: 'POST',
    data: body,
    ...(options || {}),
  });
}

/** 获取产品图片URL列表 POST /api/products/image-urls */
export async function getProductImageUrls(body: API.GetProductImageUrlsReq, options?: { [key: string]: any }) {
  return request<API.GetProductImageUrlsResp>('/api/products/image-urls', {
    method: 'POST',
    data: body,
    ...(options || {}),
  });
}

/** 修复SKU图片 POST /api/maintenance/fix-skus-images */
export async function fixSkusImages(body: API.FixSkusImagesReq, options?: { [key: string]: any }) {
  return request<API.FixSkusImagesResp>('/api/maintenance/fix-skus-images', {
    method: 'POST',
    data: body,
    ...(options || {}),
  });
}

/** 推送订单到 SCM（不取消原单） POST /api/scm/push-order-without-cancel */
export async function pushOrderToScmWithoutCancel(body: API.PushOrderToScmWithoutCancelReq, options?: { [key: string]: any }) {
  return request<API.PushOrderToScmWithoutCancelResp>('/api/scm/push-order-without-cancel', {
    method: 'POST',
    data: body,
    ...(options || {}),
  });
}

/** 获取 SCM 请求审计日志 GET /api/scm/request-audit-logs */
export async function getScmRequestAuditLogs(params: {
  region: string;
  keyword?: string;
}, options?: { [key: string]: any }) {
  return request<API.GetScmRequestAuditLogsResp>('/api/scm/request-audit-logs', {
    method: 'GET',
    params: {
      ...params,
    },
    ...(options || {}),
  });
}

/** 初始化搜索词配置 POST /api/maintenance/init-search-terms-config */
export async function initSearchTermsConfig(body: API.InitSearchTermsConfigReq, options?: { [key: string]: any }) {
  return request<API.InitSearchTermsConfigResp>('/api/maintenance/init-search-terms-config', {
    method: 'POST',
    data: body,
    ...(options || {}),
  });
}

/** 获取 oncall 配置 GET /api/oncall/config */
export async function getOncallConfig(params?: {
  email?: string;
}, options?: { [key: string]: any }) {
  return request<API.GetOncallConfigResp>('/api/oncall/config', {
    method: 'GET',
    params: {
      ...params,
    },
    ...(options || {}),
  });
}

/** 获取预签名URL GET /api/ops/oss/presigned-url */
export async function getPresignedUrl(params: {
  file_name: string;
  content_type: string;
}, options?: { [key: string]: any }) {
  return request<API.GetPresignedUrlResp>('/api/ops/oss/presigned-url', {
    method: 'GET',
    params: {
      ...params,
    },
    ...(options || {}),
  });
}

/** 获取历史APK版本 GET /api/ops/oss/ohsome-apk-versions */
export async function getOhsomeApkVersions(options?: { [key: string]: any }) {
  return request<API.GetOhsomeApkVersionsResp>('/api/ops/oss/ohsome-apk-versions', {
    method: 'GET',
    ...(options || {}),
  });
}

/** 更新 oncall 配置 PUT /api/oncall/config */
export async function updateOncallConfig(body: API.UpdateOncallConfigReq, options?: { [key: string]: any }) {
  return request<API.UpdateOncallConfigResp>('/api/oncall/config', {
    method: 'PUT',
    data: body,
    ...(options || {}),
  });
}

/** 获取当前 oncall 信息 GET /api/oncall/current */
export async function getCurrentOncall(params?: {
  email?: string;
}, options?: { [key: string]: any }) {
  return request<API.GetCurrentOncallResp>('/api/oncall/current', {
    method: 'GET',
    params: {
      ...params,
    },
    ...(options || {}),
  });
}

/** 初始化店铺商品关系 POST /api/shops/:id/init-product */
export async function initShopProduct(params: {
  id: number;
  region: string;
}, options?: { [key: string]: any }) {
  return request<API.InitShopProductResp>(`/api/shops/${params.id}/init-product`, {
    method: 'POST',
    data: {
      region: params.region,
    },
    ...(options || {}),
  });
}

/** 获取财务销售主体 GET /api/financial/sales-entity */
export async function getFinancialSalesEntity(params: {
  physical_warehouse_code: string;
}, options?: { [key: string]: any }) {
  return request<API.GetFinancialSalesEntityResp>('/api/financial/sales-entity', {
    method: 'GET',
    params: {
      ...params,
    },
    ...(options || {}),
  });
}

/** 根据订单ID获取财务销售主体 POST /api/financial/sales-entity-by-order-ids */
export async function getFinancialSalesEntityByOrderIds(body: {
  region: string;
  orderIds: string[];
}, options?: { [key: string]: any }) {
  return request<API.GetFinancialSalesEntityResp>('/api/financial/sales-entity-by-order-ids', {
    method: 'POST',
    data: body,
    ...(options || {}),
  });
}

/** 获取站点配置 GET /api/site-config */
export async function getSiteConfig(params?: {
  region?: string;
}, options?: { [key: string]: any }) {
  return request<API.GetSiteConfigResp>('/api/site-config', {
    method: 'GET',
    params: {
      ...params,
    },
    ...(options || {}),
  });
}

/** 更新站点配置 PUT /api/site-config */
export async function updateSiteConfig(body: API.UpdateSiteConfigReq, options?: { [key: string]: any }) {
  return request<API.UpdateSiteConfigResp>('/api/site-config', {
    method: 'PUT',
    data: body,
    ...(options || {}),
  });
}

/** 删除站点配置 DELETE /api/site-config/:region */
export async function deleteSiteConfig(region: string, options?: { [key: string]: any }) {
  return request<API.DeleteSiteConfigResp>(`/api/site-config/${region}`, {
    method: 'DELETE',
    ...(options || {}),
  });
}

/** 获取队列消费状态 GET /api/maintenance/queue-consumption */
export async function getQueueConsumptionStatus(
  params?: {
    region?: string;
    platform?: string;
    enabled?: string;
    current?: number;
    pageSize?: number;
  },
  options?: { [key: string]: any },
) {
  return request<API.GetQueueConsumptionStatusResp>('/api/maintenance/queue-consumption', {
    method: 'GET',
    params: {
      ...params,
    },
    ...(options || {}),
  });
}

/** 更新队列消费状态 PUT /api/maintenance/queue-consumption */
export async function updateQueueConsumptionStatus(
  body: API.UpdateQueueConsumptionStatusReq,
  options?: { [key: string]: any },
) {
  return request<API.UpdateQueueConsumptionStatusResp>('/api/maintenance/queue-consumption', {
    method: 'PUT',
    data: body,
    ...(options || {}),
  });
}

export async function syncOrderWarehouseState(
  body: API.SyncOrderWarehouseStateReq,
  options?: { [key: string]: any },
) {
  return request<API.SyncOrderWarehouseStateResp>('/api/maintenance/sync-order-warehouse-state', {
    method: 'POST',
    data: body,
    ...(options || {}),
  });
}

export async function syncShopOrderWithChannel(
  body: API.SyncShopOrderWithChannelReq,
  options?: { [key: string]: any },
) {
  return request<API.SyncShopOrderWithChannelResp>(`/api/maintenance/${body.region}/shops/${body.shopId}/orders/${body.orderId}/sync`, {
    method: 'POST',
    data: body,
    ...(options || {}),
  });
}

export async function returnMerchandiseBackWarehouse(
  body: API.ReturnMerchandiseBackWarehouseReq,
  options?: { [key: string]: any },
) {
  return request<API.ReturnMerchandiseBackWarehouseResp>(`/api/regions/${body.region}/return-request-migration/${body.warehouseInNo}`, {
    method: 'PATCH',
    data: body,
    ...(options || {}),
  });
}

/** 更新 deliver_item 销售交易主体 POST /api/regions/{region}/maintenance/deliver-item/sales-transaction */
export async function updateDeliverItemSalesTransaction(
  body: API.UpdateDeliverItemSalesTransactionReq,
  options?: { [key: string]: any },
) {
  return request<API.UpdateDeliverItemSalesTransactionResp>(`/api/regions/${body.region}/maintenance/deliver-item/sales-transaction`, {
    method: 'POST',
    data: body,
    ...(options || {}),
  });
}

/** 获取翻译列表 GET /api/i18n/translations */
export async function getTranslations(
  params: API.GetTranslationsRequest,
  options?: { [key: string]: any },
) {
  return request<API.GetTranslationsResponse>('/api/i18n/translations', {
    method: 'GET',
    params: {
      ...params,
    },
    ...(options || {}),
  });
}

/** 创建翻译 POST /api/i18n/translations */
export async function createTranslation(
  body: API.CreateTranslationRequest,
  options?: { [key: string]: any },
) {
  return request<API.Translation>('/api/i18n/translations', {
    method: 'POST',
    data: body,
    ...(options || {}),
  });
}

/** 获取翻译详情 GET /api/i18n/translations/:id */
export async function getTranslation(
  id: number,
  options?: { [key: string]: any },
) {
  return request<API.Translation>(`/api/i18n/translations/${id}`, {
    method: 'GET',
    ...(options || {}),
  });
}

/** 更新翻译 PUT /api/i18n/translations/:id */
export async function updateTranslation(
  id: number,
  body: API.UpdateTranslationRequest,
  options?: { [key: string]: any },
) {
  return request<API.Translation>(`/api/i18n/translations/${id}`, {
    method: 'PUT',
    data: body,
    ...(options || {}),
  });
}

/** 删除翻译 DELETE /api/i18n/translations/:id */
export async function deleteTranslation(
  id: number,
  options?: { [key: string]: any },
) {
  return request(`/api/i18n/translations/${id}`, {
    method: 'DELETE',
    ...(options || {}),
  });
}

/** 获取翻译值列表 GET /api/i18n/translation-values */
export async function getTranslationValues(
  params: API.GetTranslationValuesRequest,
  options?: { [key: string]: any },
) {
  return request<API.GetTranslationValuesResponse>('/api/i18n/translation-values', {
    method: 'GET',
    params: {
      ...params,
    },
    ...(options || {}),
  });
}

/** 创建翻译值 POST /api/i18n/translation-values */
export async function createTranslationValue(
  body: API.CreateTranslationValueRequest,
  options?: { [key: string]: any },
) {
  return request<API.TranslationValue>('/api/i18n/translation-values', {
    method: 'POST',
    data: body,
    ...(options || {}),
  });
}

/** 获取翻译值详情 GET /api/i18n/translation-values/:id */
export async function getTranslationValue(
  id: number,
  options?: { [key: string]: any },
) {
  return request<API.TranslationValue>(`/api/i18n/translation-values/${id}`, {
    method: 'GET',
    ...(options || {}),
  });
}

/** 更新翻译值 PUT /api/i18n/translation-values/:id */
export async function updateTranslationValue(
  id: number,
  body: API.UpdateTranslationValueRequest,
  options?: { [key: string]: any },
) {
  return request<API.TranslationValue>(`/api/i18n/translation-values/${id}`, {
    method: 'PUT',
    data: body,
    ...(options || {}),
  });
}

/** 删除翻译值 DELETE /api/i18n/translation-values/:id */
export async function deleteTranslationValue(
  id: number,
  options?: { [key: string]: any },
) {
  return request(`/api/i18n/translation-values/${id}`, {
    method: 'DELETE',
    ...(options || {}),
  });
}

/** 导出翻译 GET /api/i18n/translations/export */
export async function exportTranslations(
  params: API.ExportTranslationsRequest,
  options?: { [key: string]: any },
) {
  return request<API.ExportTranslationsResponse>('/api/i18n/translations/export', {
    method: 'GET',
    params: {
      ...params,
    },
    ...(options || {}),
  });
}

/** AI翻译 POST /api/i18n/translations/:id/ai-translate */
export async function getAITranslation(
  body: API.AITranslationRequest,
  options?: { [key: string]: any },
) {
  return request<API.AITranslationResponse>(`/api/i18n/translations/${body.id}/ai-translate`, {
    method: 'POST',
    data: body,
    ...(options || {}),
  });
}

export interface AITranslationResponse {
  data: string;
}

export interface GenerateTranslationKeyRequest {
  default_text: string;
  module: string;
  context?: string;
}

export interface GenerateTranslationKeyResponse {
  key: string;
}

/** AI生成翻译key POST /api/i18n/translations/generate-key */
export async function generateTranslationKey(
  body: API.GenerateTranslationKeyRequest,
  options?: { [key: string]: any },
) {
  return request<API.GenerateTranslationKeyResponse>('/api/i18n/translations/generate-key', {
    method: 'POST',
    data: body,
    ...(options || {}),
  });
}

/** 检查翻译Key是否存在 GET /api/i18n/translations/check-key */
export async function checkTranslationKey(
  params: API.CheckTranslationKeyRequest,
  options?: { [key: string]: any },
) {
  return request<API.CheckTranslationKeyResponse>('/api/i18n/translations/check-key', {
    method: 'GET',
    params: {
      ...params,
    },
    ...(options || {}),
  });
}

/** 从错误代码导入翻译 POST /api/i18n/translations/import-from-error-codes */
export async function importFromErrorCodes(
  body: API.ImportFromErrorCodesRequest,
  options?: { [key: string]: any },
) {
  return request<API.ImportFromErrorCodesResponse>('/api/i18n/translations/import-from-error-codes', {
    method: 'POST',
    data: body,
    ...(options || {}),
  });
}


/** 获取 influencer videos 列表 GET /api/analysis/influencer-videos */
export async function getInfluencerVideos(
  params: API.GetInfluencerVideosReq,
  options?: { [key: string]: any },
) {
  return request<API.GetInfluencerVideosResp>('/api/analysis/influencer-videos', {
    method: 'GET',
    params: {
      ...params,
    },
    ...(options || {}),
  });
}

/** 创建 influencer video POST /api/analysis/influencer-videos */
export async function createInfluencerVideo(
  body: API.InfluencerVideo,
  options?: { [key: string]: any },
) {
  return request<API.CreateInfluencerVideoResp>('/api/analysis/influencer-videos', {
    method: 'POST',
    data: body,
    ...(options || {}),
  });
}

/** 更新 influencer video PUT /api/analysis/influencer-videos/:id */
export async function updateInfluencerVideo(
  body: API.UpdateInfluencerVideoRequest,
  options?: { [key: string]: any },
) {
  return request<API.InfluencerVideo>(`/api/analysis/influencer-videos/${body.id}`, {
    method: 'PUT',
    data: body,
    ...(options || {}),
  });
}

/** 删除 influencer video DELETE /api/analysis/influencer-videos/:id */
export async function deleteInfluencerVideo(id: number, options?: { [key: string]: any }) {
  return request<Record<string, any>>(`/api/analysis/influencer-videos/${id}`, {
    method: 'DELETE',
    ...(options || {}),
  });
}

/** 获取 influencer gmv summary 列表 GET /api/analysis/influencer-gmv-summary */
export async function getInfluencerGmvSummary(
  params: API.GetInfluencerGmvSummaryReq,
  options?: { [key: string]: any },
) {
  return request<API.GetInfluencerGmvSummaryResp>('/api/analysis/influencer-gmv-summary', {
    method: 'GET',
    params: {
      ...params,
    },
    ...(options || {}),
  });
}

/** 创建 influencer gmv summary POST /api/analysis/influencer-gmv-summary */
export async function createInfluencerGmvSummary(
  body: API.CreateInfluencerGmvSummaryReq,
  options?: { [key: string]: any },
) {
  return request<API.CreateInfluencerGmvSummaryResp>('/api/analysis/influencer-gmv-summary', {
    method: 'POST',
    data: body,
    ...(options || {}),
  });
}

/** 更新 influencer gmv summary PUT /api/analysis/influencer-gmv-summary */
export async function updateInfluencerGmvSummary(
  body: API.UpdateInfluencerGmvSummaryReq,
  options?: { [key: string]: any },
) {
  return request<API.InfluencerGmvSummary>('/api/analysis/influencer-gmv-summary', {
    method: 'PUT',
    data: body,
    ...(options || {}),
  });
}

/** 获取 influencer video daily metrics 列表 GET /api/analysis/influencer-video-daily-metrics */
export async function getInfluencerVideoDailyMetrics(
  params: API.GetInfluencerVideoDailyMetricsReq,
  options?: { [key: string]: any },
) {
  return request<API.GetInfluencerVideoDailyMetricsResp>('/api/analysis/influencer-video-daily-metrics', {
    method: 'GET',
    params: {
      ...params,
    },
    ...(options || {}),
  });
}

/** 获取 influencer video datasources 列表 GET /api/analysis/influencer-video-datasources */
export async function getInfluencerVideoDatasources(
  params: API.GetInfluencerVideoDatasourcesReq,
  options?: { [key: string]: any },
) {
  return request<API.GetInfluencerVideoDatasourcesResp>('/api/analysis/influencer-video-datasources', {
    method: 'GET',
    params: {
      ...params,
    },
    ...(options || {}),
  });
}

/** 创建 influencer video datasource POST /api/analysis/influencer-video-datasources */
export async function createInfluencerVideoDatasource(
  body: API.CreateInfluencerVideoDatasourceReq,
  options?: { [key: string]: any },
) {
  return request<API.CreateInfluencerVideoDatasourceResp>('/api/analysis/influencer-video-datasources', {
    method: 'POST',
    data: body,
    ...(options || {}),
  });
}

/** 更新 influencer video datasource PUT /api/analysis/influencer-video-datasources/:id */
export async function updateInfluencerVideoDatasource(
  body: API.UpdateInfluencerVideoDatasourceReq,
  options?: { [key: string]: any },
) {
  return request<API.InfluencerVideoDatasource>(`/api/analysis/influencer-video-datasources/${body.id}`, {
    method: 'PUT',
    data: body,
    ...(options || {}),
  });
}

/** 删除 influencer video datasource DELETE /api/analysis/influencer-video-datasources/:id */
export async function deleteInfluencerVideoDatasource(id: number, options?: { [key: string]: any }) {
  return request<Record<string, any>>(`/api/analysis/influencer-video-datasources/${id}`, {
    method: 'DELETE',
    ...(options || {}),
  });
}

/** 获取 influencer product references 列表 GET /api/analysis/influencer-product-references */
export async function getInfluencerProductReferences(
  params: API.GetInfluencerProductReferencesReq,
  options?: { [key: string]: any },
) {
  return request<API.GetInfluencerProductReferencesResp>('/api/analysis/influencer-product-references', {
    method: 'GET',
    params: {
      ...params,
    },
    ...(options || {}),
  });
}

/** 创建 influencer product reference POST /api/analysis/influencer-product-references */
export async function createInfluencerProductReference(
  body: API.CreateInfluencerProductReferenceReq,
  options?: { [key: string]: any },
) {
  return request<API.CreateInfluencerProductReferenceResp>('/api/analysis/influencer-product-references', {
    method: 'POST',
    data: body,
    ...(options || {}),
  });
}

/** 获取单个 influencer product reference GET /api/analysis/influencer-product-references/:sku */
export async function getInfluencerProductReference(
  sku: string,
  options?: { [key: string]: any },
) {
  return request<API.GetInfluencerProductReferenceResp>(`/api/analysis/influencer-product-references/${sku}`, {
    method: 'GET',
    ...(options || {}),
  });
}

/** 更新 influencer product reference PUT /api/analysis/influencer-product-references/:sku */
export async function updateInfluencerProductReference(
  body: API.UpdateInfluencerProductReferenceReq,
  options?: { [key: string]: any },
) {
  return request<API.InfluencerProductReference>(`/api/analysis/influencer-product-references/${body.sku}`, {
    method: 'PUT',
    data: body,
    ...(options || {}),
  });
}

/** 删除 influencer product reference DELETE /api/analysis/influencer-product-references/:sku */
export async function deleteInfluencerProductReference(sku: string, options?: { [key: string]: any }) {
  return request<Record<string, any>>(`/api/analysis/influencer-product-references/${sku}`, {
    method: 'DELETE',
    ...(options || {}),
  });
}

/** 批量创建 influencer product references POST /api/analysis/influencer-product-references/batch */
export async function batchCreateInfluencerProductReference(
  body: API.BatchCreateInfluencerProductReferenceReq,
  options?: { [key: string]: any },
) {
  return request<API.BatchCreateInfluencerProductReferenceResp>('/api/analysis/influencer-product-references/batch', {
    method: 'POST',
    data: body,
    ...(options || {}),
  });
}

/** 获取 live performances 列表 GET /api/analysis/live-performances */
export async function getLivePerformances(
  params: API.GetLivePerformancesReq,
  options?: { [key: string]: any },
) {
  return request<API.GetLivePerformancesResp>('/api/analysis/live-performances', {
    method: 'GET',
    params: {
      ...params,
    },
    ...(options || {}),
  });
}

/** 创建 live performance POST /api/analysis/live-performances */
export async function createLivePerformance(
  body: API.CreateLivePerformanceReq,
  options?: { [key: string]: any },
) {
  return request<API.CreateLivePerformanceResp>('/api/analysis/live-performances', {
    method: 'POST',
    data: body,
    ...(options || {}),
  });
}

/** 更新 live performance PUT /api/analysis/live-performances/:id */
export async function updateLivePerformance(
  body: API.UpdateLivePerformanceReq,
  options?: { [key: string]: any },
) {
  return request<API.CreateLivePerformanceResp>(`/api/analysis/live-performances/${body.id}`, {
    method: 'PUT',
    data: body,
    ...(options || {}),
  });
}

/** 删除 live performance DELETE /api/analysis/live-performances/:id */
export async function deleteLivePerformance(
  id: number,
  options?: { [key: string]: any },
) {
  return request<Record<string, any>>(`/api/analysis/live-performances/${id}`, {
    method: 'DELETE',
    ...(options || {}),
  });
}

/** 获取 live performance datasources 列表 GET /api/analysis/live-performance-datasources */
export async function getLivePerformanceDatasources(
  params: API.GetLivePerformanceDatasourcesReq,
  options?: { [key: string]: any },
) {
  return request<API.GetLivePerformanceDatasourcesResp>('/api/analysis/live-performance-datasources', {
    method: 'GET',
    params: {
      ...params,
    },
    ...(options || {}),
  });
}

/** 创建 live performance datasource POST /api/analysis/live-performance-datasources */
export async function createLivePerformanceDatasource(
  body: API.CreateLivePerformanceDatasourceReq,
  options?: { [key: string]: any },
) {
  return request<API.CreateLivePerformanceDatasourceResp>('/api/analysis/live-performance-datasources', {
    method: 'POST',
    data: body,
    ...(options || {}),
  });
}

/** 更新 live performance datasource PUT /api/analysis/live-performance-datasources/:id */
export async function updateLivePerformanceDatasource(
  body: API.UpdateLivePerformanceDatasourceReq,
  options?: { [key: string]: any },
) {
  return request<API.LivePerformanceDatasource>(`/api/analysis/live-performance-datasources/${body.id}`, {
    method: 'PUT',
    data: body,
    ...(options || {}),
  });
}

/** 删除 live performance datasource DELETE /api/analysis/live-performance-datasources/:id */
export async function deleteLivePerformanceDatasource(id: number, options?: { [key: string]: any }) {
  return request<Record<string, any>>(`/api/analysis/live-performance-datasources/${id}`, {
    method: 'DELETE',
    ...(options || {}),
  });
}

/** 获取 live performance goals 列表 GET /api/analysis/live-performance-goals */
export async function getLivePerformanceGoals(
  params: API.GetLivePerformanceGoalsReq,
  options?: { [key: string]: any },
) {
  return request<API.GetLivePerformanceGoalsResp>('/api/analysis/live-performance-goals', {
    method: 'GET',
    params: {
      ...params,
    },
    ...(options || {}),
  });
}

/** 创建 live performance goal POST /api/analysis/live-performance-goals */
export async function createLivePerformanceGoal(
  body: API.CreateLivePerformanceGoalReq,
  options?: { [key: string]: any },
) {
  return request<API.CreateLivePerformanceGoalResp>('/api/analysis/live-performance-goals', {
    method: 'POST',
    data: body,
    ...(options || {}),
  });
}

/** 更新 live performance goal PUT /api/analysis/live-performance-goals/:month */
export async function updateLivePerformanceGoal(
  body: API.UpdateLivePerformanceGoalReq,
  options?: { [key: string]: any },
) {
  return request<API.LivePerformanceGoal>(`/api/analysis/live-performance-goals/${body.month}`, {
    method: 'PUT',
    data: body,
    ...(options || {}),
  });
}

/** 删除 live performance goal DELETE /api/analysis/live-performance-goals/:month */
export async function deleteLivePerformanceGoal(month: string, options?: { [key: string]: any }) {
  return request<Record<string, any>>(`/api/analysis/live-performance-goals/${month}`, {
    method: 'DELETE',
    ...(options || {}),
  });
}


/** 创建 release 审批申请 POST /api/release/releases/:id/approval */
export async function createReleaseApproval(
  id: number,
  body: {
    release_id: number;
    title: string;
    environment: string;
    description: string;
    approval_flow_id: number;
  },
  options?: { [key: string]: any },
) {
  return request<{
    request_id: string;
    approval_instance_id: string;
    status: string;
  }>(`/api/release/releases/${id}/approval`, {
    method: 'POST',
    data: body,
    ...(options || {}),
  });
}

/** 获取数据库列表 GET /api/databases */
export async function getDatabases(options?: { [key: string]: any }) {
  return request<API.GetDatabasesResp>('/api/release/databases', {
    method: 'GET',
    ...(options || {}),
  });
}

/** 获取数据库详情 GET /api/databases/:id */
export async function getDatabase(id: number, options?: { [key: string]: any }) {
  return request<API.Database>(`/api/release/databases/${id}`, {
    method: 'GET',
    ...(options || {}),
  });
}

/** 创建数据库 POST /api/databases */
export async function createDatabase(body: API.CreateDatabaseReq, options?: { [key: string]: any }) {
  return request<API.Database>('/api/release/databases', {
    method: 'POST',
    data: body,
    ...(options || {}),
  });
}

/** 更新数据库 PUT /api/databases/:id */
export async function updateDatabase(id: number, body: API.UpdateDatabaseReq, options?: { [key: string]: any }) {
  return request<API.Database>(`/api/release/databases/${id}`, {
    method: 'PUT',
    data: body,
    ...(options || {}),
  });
}

/** 删除数据库 DELETE /api/databases/:id */
export async function deleteDatabase(id: number, options?: { [key: string]: any }) {
  return request<API.BaseResponse>(`/api/release/databases/${id}`, {
    method: 'DELETE',
    ...(options || {}),
  });
}

/** 获取 GitLab 仓库列表 GET /api/repositories */
export async function getRepositories(params?: any, options?: { [key: string]: any }) {
  return request<{ data: API.Repository[] }>('/api/release/repositories', {
    method: 'GET',
    params,
    ...(options || {}),
  });
}

/** 获取 Jenkins 任务列表 GET /api/jenkins/jobs */
export async function getJenkinsJobs(params?: any, options?: { [key: string]: any }) {
  return request<API.GetJenkinsJobsResp>('/api/release/jenkins/jobs', {
    method: 'GET',
    params,
    ...(options || {}),
  });
}

/** 获取 GitLab 仓库详情 GET /api/repositories/:id */
export async function getRepository(id: number, options?: { [key: string]: any }) {
  return request<API.Repository>(`/api/release/repositories/${id}`, {
    method: 'GET',
    ...(options || {}),
  });
}

/** 创建 GitLab 仓库 POST /api/repositories */
export async function createRepository(body: Partial<API.Repository>, options?: { [key: string]: any }) {
  return request<API.Repository>('/api/release/repositories', {
    method: 'POST',
    data: body,
    ...(options || {}),
  });
}

/** 更新 GitLab 仓库 PUT /api/repositories/:id */
export async function updateRepository(id: number, body: Partial<API.Repository>, options?: { [key: string]: any }) {
  return request<API.Repository>(`/api/release/repositories/${id}`, {
    method: 'PUT',
    data: body,
    ...(options || {}),
  });
}

/** 删除 GitLab 仓库 DELETE /api/repositories/:id */
export async function deleteRepository(id: number, options?: { [key: string]: any }) {
  return request<API.BaseResponse>(`/api/release/repositories/${id}`, {
    method: 'DELETE',
    ...(options || {}),
  });
}

/** 获取 Jenkins 任务详情 GET /api/jenkins/jobs/:id */
export async function getJenkinsJob(id: number, options?: { [key: string]: any }) {
  return request<API.JenkinsJob>(`/api/release/jenkins/jobs/${id}`, {
    method: 'GET',
    ...(options || {}),
  });
}

/** 创建 Jenkins 任务 POST /api/jenkins/jobs */
export async function createJenkinsJob(body: Partial<API.JenkinsJob>, options?: { [key: string]: any }) {
  return request<API.JenkinsJob>('/api/release/jenkins/jobs', {
    method: 'POST',
    data: body,
    ...(options || {}),
  });
}

/** 更新 Jenkins 任务 PUT /api/jenkins/jobs/:id */
export async function updateJenkinsJob(id: number, body: Partial<API.JenkinsJob>, options?: { [key: string]: any }) {
  return request<API.JenkinsJob>(`/api/release/jenkins/jobs/${id}`, {
    method: 'PUT',
    data: body,
    ...(options || {}),
  });
}

/** 删除 Jenkins 任务 DELETE /api/jenkins/jobs/:id */
export async function deleteJenkinsJob(id: number, options?: { [key: string]: any }) {
  return request<API.BaseResponse>(`/api/release/jenkins/jobs/${id}`, {
    method: 'DELETE',
    ...(options || {}),
  });
}

/** 添加 release 分支 POST /api/releases/:id/branches */
export async function addReleaseBranch(
  id: number,
  body: API.AddReleaseBranchReq,
  options?: { [key: string]: any },
) {
  return request<API.ReleaseBranch>(`/api/release/releases/${id}/branches`, {
    method: 'POST',
    data: body,
    ...(options || {}),
  });
}

/** 获取 release 分支列表 GET /api/releases/:id/branches */
export async function getReleaseBranches(
  id: number,
  options?: { [key: string]: any },
) {
  return request<API.ReleaseBranch[]>(`/api/release/releases/${id}/branches`, {
    method: 'GET',
    ...(options || {}),
  });
}

/** 添加 release Jenkins 任务 POST /api/releases/:id/jenkins */
export async function addReleaseJenkinsJob(
  id: number,
  body: API.AddReleaseJenkinsJobReq,
  options?: { [key: string]: any },
) {
  return request<API.ReleaseJenkinsJob>(`/api/release/releases/${id}/jenkins`, {
    method: 'POST',
    data: body,
    ...(options || {}),
  });
}

/** 获取 release Jenkins 任务列表 GET /api/releases/:id/jenkins */
export async function getReleaseJenkinsJobs(
  id: number,
  options?: { [key: string]: any },
) {
  return request<API.ReleaseJenkinsJob[]>(`/api/release/releases/${id}/jenkins`, {
    method: 'GET',
    ...(options || {}),
  });
}


/** 添加审核人 POST /api/releases/:id/reviewers */
export async function addReviewer(
  id: number,
  body: API.AddReviewerReq,
  options?: { [key: string]: any },
) {
  return request<API.Reviewer>(`/api/release/releases/${id}/reviewers`, {
    method: 'POST',
    data: body,
    ...(options || {}),
  });
}

/** 获取审核人列表 GET /api/releases/:id/reviewers */
export async function getReviewers(
  id: number,
  options?: { [key: string]: any },
) {
  return request<API.Reviewer[]>(`/api/release/releases/${id}/reviewers`, {
    method: 'GET',
    ...(options || {}),
  });
}

/** 提交审核 POST /api/releases/:id/review */
export async function review(
  id: number,
  body: API.ReviewReq,
  options?: { [key: string]: any },
) {
  return request<API.Reviewer>(`/api/release/releases/${id}/review`, {
    method: 'POST',
    data: body,
    ...(options || {}),
  });
}


// Kubernetes Namespace APIs
/** 获取命名空间列表 GET /api/namespaces */
export async function getNamespaces(
  params: API.GetNamespacesReq,
  options?: { [key: string]: any },
) {
  return request<API.GetNamespacesResp>('/api/release/namespaces', {
    method: 'GET',
    params,
    ...(options || {}),
  });
}

/** 获取命名空间详情 GET /api/namespaces/:id */
export async function getNamespace(
  id: number,
  options?: { [key: string]: any },
) {
  return request<API.KubernetesNamespace>(`/api/release/namespaces/${id}`, {
    method: 'GET',
    ...(options || {}),
  });
}

/** 创建命名空间 POST /api/namespaces */
export async function createNamespace(
  body: API.CreateNamespaceReq,
  options?: { [key: string]: any },
) {
  return request<API.KubernetesNamespace>('/api/release/namespaces', {
    method: 'POST',
    data: body,
    ...(options || {}),
  });
}

/** 更新命名空间 PUT /api/namespaces/:id */
export async function updateNamespace(
  id: number,
  body: API.UpdateNamespaceReq,
  options?: { [key: string]: any },
) {
  return request<API.KubernetesNamespace>(`/api/release/namespaces/${id}`, {
    method: 'PUT',
    data: body,
    ...(options || {}),
  });
}

/** 删除命名空间 DELETE /api/namespaces/:id */
export async function deleteNamespace(
  id: number,
  options?: { [key: string]: any },
) {
  return request<API.BaseResponse>(`/api/release/namespaces/${id}`, {
    method: 'DELETE',
    ...(options || {}),
  });
}

/** 获取应用列表 GET /api/release/applications */
export async function getApplications(params?: any, options?: { [key: string]: any }) {
  return request<{ data: API.Application[] }>('/api/release/applications', {
    method: 'GET',
    params,
    ...(options || {}),
  });
}

/** 获取应用详情 GET /api/release/applications/:id */
export async function getApplication(id: number, options?: { [key: string]: any }) {
  return request<{ data: API.Application }>(`/api/release/applications/${id}`, {
    method: 'GET',
    ...(options || {}),
  });
}

/** 创建应用 POST /api/release/applications */
export async function createApplication(body: Partial<API.Application>, options?: { [key: string]: any }) {
  return request<API.Application>('/api/release/applications', {
    method: 'POST',
    data: body,
    ...(options || {}),
  });
}

/** 更新应用 PUT /api/release/applications/:id */
export async function updateApplication(id: number, body: Partial<API.Application>, options?: { [key: string]: any }) {
  return request<API.Application>(`/api/release/applications/${id}`, {
    method: 'PUT',
    data: body,
    ...(options || {}),
  });
}

/** 删除应用 DELETE /api/release/applications/:id */
export async function deleteApplication(id: number, options?: { [key: string]: any }) {
  return request<API.BaseResponse>(`/api/release/applications/${id}`, {
    method: 'DELETE',
    ...(options || {}),
  });
}

/** 获取 GitLab 分支列表 GET /api/release/repositories/:id/branches */
export async function getGitLabBranches(
  repositoryId: number,
  options?: { [key: string]: any },
) {
  return request<API.GetGitLabBranchesResp>(`/api/release/repositories/${repositoryId}/branches`, {
    method: 'GET',
    ...(options || {}),
  });
}

/** 获取 ConfigMap 列表 GET /api/k8s/configmaps */
export async function getConfigMaps(
  params: API.GetConfigMapsReq,
  options?: { [key: string]: any },
) {
  return request<API.GetConfigMapsResp>('/api/release/k8s/configmaps', {
    method: 'GET',
    params,
    ...(options || {}),
  });
}

/** 获取 ConfigMap 内容 GET /api/k8s/configmaps/content */
export async function getConfigMapContent(
  params: API.GetConfigMapContentReq,
  options?: { [key: string]: any },
) {
  return request<API.GetConfigMapContentResp>('/api/release/k8s/configmaps/content', {
    method: 'GET',
    params,
    ...(options || {}),
  });
}

/** 获取角色列表 GET /api/acl/roles */
export async function getRoles(options?: { [key: string]: any }) {
  return request<API.GetRolesResp>('/api/acl/roles', {
    method: 'GET',
    ...(options || {}),
  });
}

/** 创建角色 POST /api/acl/roles */
export async function createRole(body: API.CreateRoleReq, options?: { [key: string]: any }) {
  return request<API.CreateRoleResp>('/api/acl/roles', {
    method: 'POST',
    data: body,
    ...(options || {}),
  });
}

/** 更新角色 PUT /api/acl/roles/:name */
export async function updateRole(name: string, body: API.UpdateRoleReq, options?: { [key: string]: any }) {
  return request<API.UpdateRoleResp>(`/api/acl/roles/${name}`, {
    method: 'PUT',
    data: body,
    ...(options || {}),
  });
}

/** 删除角色 DELETE /api/acl/roles/:name */
export async function deleteRole(name: string, options?: { [key: string]: any }) {
  return request<API.DeleteRoleResp>(`/api/acl/roles/${name}`, {
    method: 'DELETE',
    ...(options || {}),
  });
}

/** 获取可用权限路径 GET /api/acl/available-paths */
export async function getAvailablePaths(options?: { [key: string]: any }) {
  return request<API.GetAvailablePathsResp>('/api/acl/available-paths', {
    method: 'GET',
    ...(options || {}),
  });
}

export async function executeDMSQuery(body: API.DMSQueryReq, options?: { [key: string]: any }) {
  return request<API.DMSQueryResult>('/api/ops/dms/query', {
    method: 'POST',
    data: body,
    ...(options || {}),
  });
}

export async function executeDMSMutation(body: API.ExecuteDMSMutationReq, options?: { [key: string]: any }) {
  return request<API.ExecuteDMSMutationResp>('/api/ops/dms/mutation', {
    method: 'POST',
    data: body,
    ...(options || {}),
  });
}


export async function getWarehouseStockSyncs(params: {
  region?: string;
  warehouseCode?: string;
  notifyRegion?: string;
}) {
  return request('/api/warehouse-stock-syncs', {
    method: 'GET',
    params,
  });
}

export async function createWarehouseStockSync(data: {
  region: string;
  warehouseCode: string;
  notifyRegion: string;
}) {
  return request('/api/warehouse-stock-syncs', {
    method: 'POST',
    data,
  });
}

export async function updateWarehouseStockSync(data: {
  id: number;
  region: string;
  notifyRegion: string;
}) {
  return request(`/api/regions/${data.region}/warehouse-stock-syncs/${data.id}`, {
    method: 'PUT',
    data,
  });
}

export async function deleteWarehouseStockSync(data: {
  id: number;
  region: string;
}) {
  return request(`/api/regions/${data.region}/warehouse-stock-syncs/${data.id}`, {
    method: 'DELETE',
    data,
  });
}

// ==================== Approval 相关 API ====================

// 审批流程模板管理
/** 创建审批流程 POST /api/approval/flows */
export async function createApprovalFlow(body: API.CreateApprovalFlowReq, options?: { [key: string]: any }) {
  return request<API.ApprovalFlow>('/api/approval/flows', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 获取审批流程列表 GET /api/approval/flows */
export async function getApprovalFlows(params: API.GetApprovalFlowsReq, options?: { [key: string]: any }) {
  return request<API.GetApprovalFlowsResp>('/api/approval/flows', {
    method: 'GET',
    params: {
      ...params,
    },
    ...(options || {}),
  });
}

/** 获取审批流程详情 GET /api/approval/flows/:id */
export async function getApprovalFlow(id: number, options?: { [key: string]: any }) {
  return request<API.ApprovalFlow>(`/api/approval/flows/${id}`, {
    method: 'GET',
    ...(options || {}),
  });
}

/** 更新审批流程 PUT /api/approval/flows/:id */
export async function updateApprovalFlow(id: number, body: API.UpdateApprovalFlowReq, options?: { [key: string]: any }) {
  return request<API.ApprovalFlow>(`/api/approval/flows/${id}`, {
    method: 'PUT',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 删除审批流程 DELETE /api/approval/flows/:id */
export async function deleteApprovalFlow(id: number, options?: { [key: string]: any }) {
  return request<API.BaseResponse>(`/api/approval/flows/${id}`, {
    method: 'DELETE',
    ...(options || {}),
  });
}

// 发布申请相关
/** 创建发布申请 POST /api/approval/release-requests */
export async function createReleaseRequest(body: API.CreateReleaseRequestReq, options?: { [key: string]: any }) {
  return request<API.ReleaseRequest>('/api/approval/release-requests', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 获取我的发布申请列表 GET /api/approval/release-requests/me */
export async function getMyReleaseRequests(params: API.GetMyReleaseRequestsReq, options?: { [key: string]: any }) {
  return request<API.GetMyReleaseRequestsResp>('/api/approval/release-requests/me', {
    method: 'GET',
    params: {
      ...params,
    },
    ...(options || {}),
  });
}

/** 获取发布申请详情 GET /api/approval/release-requests/:id */
export async function getReleaseRequest(id: string, options?: { [key: string]: any }) {
  return request<API.ReleaseRequest>(`/api/approval/release-requests/${id}`, {
    method: 'GET',
    ...(options || {}),
  });
}

/** 获取审批进度 GET /api/approval/release-requests/:request_id/approval-progress */
export async function getApprovalProgress(requestId: string, options?: { [key: string]: any }) {
  return request<API.ApprovalProgress>(`/api/approval/release-requests/${requestId}/approval-progress`, {
    method: 'GET',
    ...(options || {}),
  });
}

// 审批任务相关
/** 获取我的审批任务列表 GET /api/approval/tasks/me */
export async function getMyApprovalTasks(params: API.GetMyApprovalTasksReq, options?: { [key: string]: any }) {
  return request<API.GetMyApprovalTasksResp>('/api/approval/tasks/me', {
    method: 'GET',
    params: {
      ...params,
    },
    ...(options || {}),
  });
}

/** 获取全部审批任务列表 GET /api/approval/tasks */
export async function getAllApprovalTasks(params: API.GetAllApprovalTasksReq, options?: { [key: string]: any }) {
  return request<API.GetAllApprovalTasksResp>('/api/approval/tasks', {
    method: 'GET',
    params: {
      ...params,
    },
    ...(options || {}),
  });
}

/** 获取审批任务详情 GET /api/approval/tasks/:id */
export async function getApprovalTask(id: string, options?: { [key: string]: any }) {
  return request<API.ApprovalTask>(`/api/approval/tasks/${id}`, {
    method: 'GET',
    ...(options || {}),
  });
}

/** 提交审批 POST /api/approval/tasks/:task_id/approve */
export async function submitApproval(taskId: string, body: API.SubmitApprovalReq, options?: { [key: string]: any }) {
  return request<API.SubmitApprovalResp>(`/api/approval/tasks/${taskId}/approve`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 审批通过 POST /api/approval/tasks/:task_id/approve */
export async function approveTask(taskId: string, body: API.ApproveTaskReq, options?: { [key: string]: any }) {
  return request<API.ApproveTaskResp>(`/api/approval/tasks/${taskId}/approve`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 审批拒绝 POST /api/approval/tasks/:task_id/reject */
export async function rejectTask(taskId: string, body: API.RejectTaskReq, options?: { [key: string]: any }) {
  return request<API.RejectTaskResp>(`/api/approval/tasks/${taskId}/reject`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 跳过审批任务 POST /api/approval/tasks/:task_id/skip */
export async function skipTask(taskId: string, body: API.SkipTaskReq, options?: { [key: string]: any }) {
  return request<API.SkipTaskResp>(`/api/approval/tasks/${taskId}/skip`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 获取审批实例详情 GET /api/approval/instances/:id */
export async function getApprovalInstance(id: string, options?: { [key: string]: any }) {
  return request<API.ApprovalInstance>(`/api/approval/instances/${id}`, {
    method: 'GET',
    ...(options || {}),
  });
}

/** 获取审批实例的所有任务 GET /api/approval/instances/:id/tasks */
export async function getApprovalInstanceTasks(id: string, options?: { [key: string]: any }) {
  return request<API.GetApprovalInstanceTasksResp>(`/api/approval/instances/${id}/tasks`, {
    method: 'GET',
    ...(options || {}),
  });
}

// Release Plans related APIs
/** 创建发布计划 POST /api/release/plans */
export async function createReleasePlan(
  body: API.CreateReleasePlanReq,
  options?: { [key: string]: any },
) {
  return request<API.ReleasePlan>('/api/release/plans', {
    method: 'POST',
    data: body,
    ...(options || {}),
  });
}

/** 获取发布计划列表 GET /api/release/plans */
export async function getReleasePlans(
  params: API.GetReleasePlansReq,
  options?: { [key: string]: any },
) {
  return request<API.GetReleasePlansResp>('/api/release/plans', {
    method: 'GET',
    params: {
      ...params,
    },
    ...(options || {}),
  });
}

/** 获取发布计划详情 GET /api/release/plans/:plan_id */
export async function getReleasePlan(
  planId: string,
  options?: { [key: string]: any },
) {
  return request<API.ReleasePlan>(`/api/release/plans/${planId}`, {
    method: 'GET',
    ...(options || {}),
  });
}

/** 更新发布计划状态 PATCH /api/release/plans/:plan_id/status */
export async function updateReleasePlanStatus(
  planId: string,
  body: { status: string },
  options?: { [key: string]: any },
) {
  return request<API.BaseResponse>(`/api/release/plans/${planId}/status`, {
    method: 'PATCH',
    data: body,
    ...(options || {}),
  });
}

/** 创建发布计划审批 POST /api/release/plans/:plan_id/approvals */
export async function createReleasePlanApproval(
  planId: string,
  options?: { [key: string]: any },
) {
  return request<API.CreateReleaseApprovalResp>(`/api/release/plans/${planId}/approvals`, {
    method: 'POST',
    ...(options || {}),
  });
}

/** 执行应用部署任务 POST /api/release/application-tasks/:task_id/exec */
export async function executeApplicationTask(
  taskId: string,
  options?: { [key: string]: any },
) {
  return request<API.ExecuteTaskResp>(`/api/release/application-tasks/${taskId}/exec`, {
    method: 'POST',
    ...(options || {}),
  });
}

/** 执行数据库迁移任务 POST /api/release/db-migration-tasks/:task_id/exec */
export async function executeDbMigrationTask(
  taskId: string,
  options?: { [key: string]: any },
) {
  return request<API.ExecuteTaskResp>(`/api/release/db-migration-tasks/${taskId}/exec`, {
    method: 'POST',
    ...(options || {}),
  });
}

/** 执行所有任务 POST /api/release/plans/:plan_id/tasks/exec */
export async function executeAllTasks(
  planId: string,
  options?: { [key: string]: any },
) {
  return request<API.ExecuteAllTasksResp>(`/api/release/plans/${planId}/tasks/exec`, {
    method: 'POST',
    ...(options || {}),
  });
}

/** 获取应用部署任务详情 GET /api/release/application-tasks/:task_id */
export async function getApplicationTask(
  taskId: string,
  options?: { [key: string]: any },
) {
  return request<API.ReleaseApplicationTask>(`/api/release/application-tasks/${taskId}`, {
    method: 'GET',
    ...(options || {}),
  });
}

/** 获取发布计划审批列表 GET /api/release/plans/:plan_id/approvals */
export async function getReleasePlanApprovals(
  planId: string,
  options?: { [key: string]: any },
) {
  return request<API.ApprovalInstance[]>(`/api/release/plans/${planId}/approvals`, {
    method: 'GET',
    ...(options || {}),
  });
}

/** 获取发布计划单个审批详情 GET /api/release/plans/:plan_id/approvals/:approval_id */
export async function getReleasePlanApproval(
  planId: string,
  approvalId: string,
  options?: { [key: string]: any },
) {
  return request<API.ApprovalInstance>(`/api/release/plans/${planId}/approvals/${approvalId}`, {
    method: 'GET',
    ...(options || {}),
  });
}

/** 获取 XXL-JOB 执行器列表 GET /api/ops/xxl/executors */
export async function listXxlExecutors(options?: { [key: string]: any }) {
  return request<API.ListXxlExecutorsResp>('/api/ops/xxl/executors', {
    method: 'GET',
    ...(options || {}),
  });
}

/** 从印尼复制任务 POST /api/ops/xxl/executors/:id/copy-jobs-from-indonesia */
export async function copyJobsFromIndonesia(params: API.CopyJobsFromIndonesiaReq, options?: { [key: string]: any }) {
  const { id } = params;
  return request<API.CopyJobsFromIndonesiaResp>(`/api/ops/xxl/executors/${id}/copy-jobs-from-indonesia`, {
    method: 'POST',
    data: params,
    ...(options || {}),
  });
}

/** 获取 O2O Grab 配置列表 GET /api/ops/o2o/grab/config/list */
export async function getO2OGrabConfigList(options?: { [key: string]: any }) {
  return request<API.GetO2OGrabConfigListResp>('/api/ops/o2o/grab/config/list', {
    method: 'GET',
    ...(options || {}),
  });
}

/** 添加 O2O Grab 配置 POST /api/ops/o2o/grab/config/add */
export async function addO2OGrabConfig(body: API.AddO2OGrabConfigReq, options?: { [key: string]: any }) {
  return request<API.AddO2OGrabConfigResp>('/api/ops/o2o/grab/config/add', {
    method: 'POST',
    data: body,
    ...(options || {}),
  });
}

/** 更新 O2O Grab 配置 POST /api/ops/o2o/grab/config/update */
export async function updateO2OGrabConfig(body: API.UpdateO2OGrabConfigReq, options?: { [key: string]: any }) {
  return request<API.UpdateO2OGrabConfigResp>('/api/ops/o2o/grab/config/update', {
    method: 'POST',
    data: body,
    ...(options || {}),
  });
}

/** 删除 O2O Grab 配置 POST /api/ops/o2o/grab/config/delete */
export async function deleteO2OGrabConfig(body: API.DeleteO2OGrabConfigReq, options?: { [key: string]: any }) {
  return request<API.DeleteO2OGrabConfigResp>('/api/ops/o2o/grab/config/delete', {
    method: 'POST',
    data: body,
    ...(options || {}),
  });
}

/** 图片去Logo处理 POST /api/ops/image/remove-logo */
export async function removeLogoFromImage(body: API.RemoveLogoFromImageReq, options?: { [key: string]: any }) {
  return request<API.RemoveLogoFromImageResp>('/api/ops/image/remove-logo', {
    method: 'POST',
    data: body,
    ...(options || {}),
  });
}

/** 图片去Logo处理V2 POST /api/ops/image/remove-logo-v2 */
export async function removeLogoFromImageV2(body: API.RemoveLogoFromImageReq, options?: { [key: string]: any }) {
  return request<API.RemoveLogoFromImageResp>('/api/ops/image/remove-logo-v2', {
    method: 'POST',
    data: body,
    ...(options || {}),
  });
}

/** 获取Funifun奖励商品列表 GET /api/ohsome/funifun/reward-items */
export async function listFunifunRewardItems(
  params: {
    region?: string;
    page?: number;
    page_size?: number;
  },
  options?: { [key: string]: any }
) {
  return request<API.ListFunifunRewardItemsResp>('/api/ohsome/funifun/reward-items', {
    method: 'GET',
    params,
    ...(options || {}),
  });
}

/** 同步Funifun奖励商品 POST /api/ohsome/regions/:region/funifun/reward-items/:id/sync/:targetRegion */
export async function syncFunifunRewardItems(
  params: {
    region: string;
    id: number;
    targetRegion: string;
  },
  options?: { [key: string]: any }
) {
  return request<API.SyncFunifunRewardItemsResp>(`/api/ohsome/regions/${params.region}/funifun/reward-items/${params.id}/sync/${params.targetRegion}`, {
    method: 'POST',
    ...(options || {}),
  });
}

/** 获取特性开关配置 GET /api/ops/feature-control/config */
export async function getFeatureControlConfig(options?: { [key: string]: any }) {
  return request<API.GetFeatureControlConfigResp>('/api/ops/feature-control/config', {
    method: 'GET',
    ...(options || {}),
  });
}

/** 更新特性开关配置 POST /api/ops/feature-control/config */
export async function updateFeatureControlConfig(body: API.UpdateFeatureControlConfigReq, options?: { [key: string]: any }) {
  return request<API.UpdateFeatureControlConfigResp>('/api/ops/feature-control/config', {
    method: 'POST',
    data: body,
    ...(options || {}),
  });
}

