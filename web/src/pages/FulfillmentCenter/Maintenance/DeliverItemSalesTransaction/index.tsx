import React, { useState } from 'react';
import {
  <PERSON><PERSON><PERSON><PERSON>,
  ProCard,
} from '@ant-design/pro-components';
import {
  Button,
  message,
  Upload,
  Progress,
  Alert,
  List,
  Space,
  Card,
  Typography,
  Select,
  Form,
} from 'antd';
import {
  UploadOutlined,
  FileExcelOutlined,
  ExclamationCircleOutlined,
} from '@ant-design/icons';
import <PERSON> from 'papaparse';
import { updateDeliverItemSalesTransaction } from '@/services/ant-design-pro/api';

const { Title, Text } = Typography;

interface CSVRow {
  rel_order_no: string;
  sku: string;
  subject_code: string;
  change_quantity: number;
  create_time: string;
  transaction_link_code: string;
}

interface ProcessResult {
  success: boolean;
  message: string;
  row: CSVRow;
}

const DeliverItemSalesTransactionMaintenance: React.FC = () => {
  const [isProcessing, setIsProcessing] = useState(false);
  const [processProgress, setProcessProgress] = useState(0);
  const [processStatus, setProcessStatus] = useState('');
  const [results, setResults] = useState<ProcessResult[]>([]);
  const [showResults, setShowResults] = useState(false);
  const [selectedRegion, setSelectedRegion] = useState<string>('');

  // Region options
  const regionOptions = [
    { label: 'TH (Thailand)', value: 'th' },
    { label: 'VN (Vietnam)', value: 'vn' },
    { label: 'MY (Malaysia)', value: 'my' },
    { label: 'SG (Singapore)', value: 'sg' },
    { label: 'PH (Philippines)', value: 'ph' },
    { label: 'ID (Indonesia)', value: 'id' },
  ];

  // Generate CSV template
  const generateTemplate = () => {
    const headers = [
      'rel_order_no',
      'sku',
      'subject_code',
      'change_quantity',
      'create_time',
      'transaction_link_code'
    ];

    const exampleData = [
      'ORD2025010100001',
      '23070800210003',
      '109',
      '1',
      '2025-01-01 10:00:00',
      'TC2025010100001-001'
    ];

    const csvContent = Papa.unparse({
      fields: headers,
      data: [exampleData]
    });

    const blob = new Blob(['\ufeff' + csvContent], { type: 'text/csv;charset=utf-8;' });
    const link = document.createElement('a');
    link.href = window.URL.createObjectURL(blob);
    link.download = 'deliver_item_sales_transaction_template.csv';
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
  };

  // Process uploaded CSV file
  const processUploadedFile = async (file: File) => {
    // Check if region is selected
    if (!selectedRegion) {
      message.error('Please select a region before uploading the CSV file.');
      return;
    }

    try {
      const text = await file.text();

      Papa.parse(text, {
        header: true,
        skipEmptyLines: true,
        complete: async (parseResults) => {
          if (parseResults.errors.length > 0) {
            message.error('CSV parsing failed. Please check the file format.');
            console.error('CSV parsing errors:', parseResults.errors);
            return;
          }

          const rows = parseResults.data as CSVRow[];
          const totalRows = rows.length;

          if (totalRows === 0) {
            message.error('CSV file is empty or has no valid data rows.');
            return;
          }

          setIsProcessing(true);
          setProcessProgress(0);
          setProcessStatus(`开始处理 ${totalRows} 条记录...`);
          setResults([]);
          setShowResults(false);

          const processResults: ProcessResult[] = [];
          let successCount = 0;

          for (let i = 0; i < rows.length; i++) {
            const row = rows[i];
            const progress = Math.round(((i + 1) / totalRows) * 100);
            
            setProcessProgress(progress);
            setProcessStatus(`处理第 ${i + 1}/${totalRows} 条记录: ${row.rel_order_no} - ${row.sku}`);

            try {
              // Validate required fields
              if (!row.rel_order_no || !row.sku || !row.subject_code || !row.change_quantity || !row.create_time || !row.transaction_link_code) {
                throw new Error('Missing required fields');
              }

              // Call API to update deliver item
              const response = await updateDeliverItemSalesTransaction({
                region: selectedRegion,
                relOrderNo: row.rel_order_no,
                sku: row.sku,
                subjectCode: row.subject_code,
                changeQuantity: parseInt(row.change_quantity.toString()),
                createTime: row.create_time,
                transactionLinkCode: row.transaction_link_code,
              });

              if (response.success) {
                successCount++;
                processResults.push({
                  success: true,
                  message: response.message || 'Success',
                  row,
                });
              } else {
                processResults.push({
                  success: false,
                  message: response.message || 'Unknown error',
                  row,
                });
              }
            } catch (error: any) {
              processResults.push({
                success: false,
                message: error.message || 'Request failed',
                row,
              });
            }

            // Add small delay to prevent overwhelming the server
            if (i < rows.length - 1) {
              await new Promise(resolve => setTimeout(resolve, 100));
            }
          }

          setResults(processResults);
          setShowResults(true);
          setIsProcessing(false);
          setProcessStatus(`处理完成！成功: ${successCount}, 失败: ${totalRows - successCount}`);

          if (successCount === totalRows) {
            message.success(`所有 ${totalRows} 条记录处理成功！`);
          } else if (successCount > 0) {
            message.warning(`处理完成：${successCount} 条成功，${totalRows - successCount} 条失败`);
          } else {
            message.error('所有记录处理失败，请检查数据格式和内容');
          }
        },
        error: (error: any) => {
          console.error('CSV parsing error:', error);
          message.error('Failed to parse CSV file.');
          setIsProcessing(false);
        }
      });
    } catch (error) {
      console.error('Error processing file:', error);
      message.error('Failed to process file. Please check the file format.');
      setIsProcessing(false);
    }
  };

  const successResults = results.filter(r => r.success);
  const failedResults = results.filter(r => !r.success);

  return (
    <PageContainer
      title="Deliver Item Sales Transaction Maintenance"
      subTitle="Update deliver_item sales_transaction_master field via CSV upload"
    >
      <ProCard>
        <Space direction="vertical" style={{ width: '100%' }} size="large">
          <Card title="Step 1: Select Region">
            <Form layout="inline">
              <Form.Item label="Region" required>
                <Select
                  style={{ width: 200 }}
                  placeholder="Select a region"
                  value={selectedRegion}
                  onChange={setSelectedRegion}
                  options={regionOptions}
                />
              </Form.Item>
            </Form>
            <Text type="secondary" style={{ marginTop: 8, display: 'block' }}>
              Please select the region for which you want to update deliver item sales transactions.
            </Text>
          </Card>

          <Card title="Step 2: Download Template">
            <Space>
              <Button icon={<FileExcelOutlined />} onClick={generateTemplate}>
                Download CSV Template
              </Button>
              <Text type="secondary">
                Download the template and fill in your data with the required fields
              </Text>
            </Space>
          </Card>

          <Card title="Step 3: Upload CSV File">
            <Upload.Dragger
              accept=".csv"
              beforeUpload={(file) => {
                processUploadedFile(file);
                return false;
              }}
              maxCount={1}
              disabled={isProcessing || !selectedRegion}
            >
              <p className="ant-upload-drag-icon">
                <UploadOutlined />
              </p>
              <p className="ant-upload-text">Click or drag CSV file to this area to upload</p>
              <p className="ant-upload-hint">
                Please select a region first, then make sure to use the correct template format with fields: rel_order_no, sku, subject_code, change_quantity, create_time, transaction_link_code
              </p>
            </Upload.Dragger>

            {isProcessing && (
              <div style={{ marginTop: 16 }}>
                <Progress
                  percent={processProgress}
                  status={processProgress === 100 ? 'success' : 'active'}
                />
                <Text style={{ marginTop: 8, display: 'block' }}>{processStatus}</Text>
              </div>
            )}
          </Card>

          {showResults && (
            <Card title="Processing Results">
              <Space direction="vertical" style={{ width: '100%' }}>
                <Alert
                  message={`Processing completed: ${successResults.length} successful, ${failedResults.length} failed`}
                  type={failedResults.length === 0 ? 'success' : failedResults.length < results.length ? 'warning' : 'error'}
                  showIcon
                />

                {failedResults.length > 0 && (
                  <div>
                    <Title level={5}>
                      <ExclamationCircleOutlined style={{ color: '#ff4d4f', marginRight: 8 }} />
                      Failed Records ({failedResults.length})
                    </Title>
                    <List
                      size="small"
                      bordered
                      dataSource={failedResults}
                      renderItem={(item) => (
                        <List.Item>
                          <Space direction="vertical" style={{ width: '100%' }}>
                            <Text strong>
                              {item.row.rel_order_no} - {item.row.sku}
                            </Text>
                            <Text type="danger">{item.message}</Text>
                          </Space>
                        </List.Item>
                      )}
                      style={{ maxHeight: 300, overflow: 'auto' }}
                    />
                  </div>
                )}

                {successResults.length > 0 && (
                  <div>
                    <Title level={5} style={{ color: '#52c41a' }}>
                      Successful Records ({successResults.length})
                    </Title>
                    <Text type="secondary">
                      All successful records have been processed and updated in the database.
                    </Text>
                  </div>
                )}
              </Space>
            </Card>
          )}
        </Space>
      </ProCard>
    </PageContainer>
  );
};

export default DeliverItemSalesTransactionMaintenance;
